# 學生答題投資系統 - 演示指南

## 快速體驗

1. **打開系統**: 雙擊 `index.html` 文件，或用瀏覽器打開
2. **查看示例數據**: 系統已預載10位學生的示例資料
3. **開始投資**: 選擇一個座號，然後選擇3位同學進行投資

## 演示流程

### 第一步：學生投資體驗
1. 點擊任意學生座號（例如：座號1 王小明）
2. 在投資區選擇3位同學（例如：座號5、13、16）
3. 點擊「提交投資」按鈕
4. 查看成功提示訊息
5. 觀察排行榜中的「投資者姓名」欄位顯示投資者的真實姓名

### 第二步：查看激勵訊息
1. 選擇不同分數的學生
2. 觀察激勵面板中的個人化訊息
3. 點擊「換個激勵訊息」按鈕體驗隨機訊息

### 第三步：管理員功能
1. 點擊右上角「管理員」按鈕
2. **學生管理**：
   - 查看26位學生列表
   - 新增學生：輸入座號27，姓名「新同學」
   - 刪除學生（可選）
3. **分數管理**：
   - 選擇學生更新分數
   - 嘗試批量更新：
     ```
     1,95
     2,88
     3,92
     5,98
     13,94
     16,97
     ```
4. **結算系統**：
   - 點擊「執行週結算」按鈕
   - 系統會自動計算投資收益
   - 前3名學生的投資者各獲得100點獎勵
5. **歷史記錄**：
   - 切換到「歷史記錄」標籤
   - 選擇週次查看結算結果
   - 查看前三名和投資收益詳情
6. **數據管理**：
   - 匯出數據到 JSON 文件
   - 查看匯出的數據結構

### 第四步：主題切換
1. 點擊右上角「暗色」按鈕
2. 體驗暗色主題
3. 再次點擊切換回亮色主題

## 教學重點

### 前端技術展示
- **HTML5 語義化標籤**: 觀察頁面結構
- **CSS3 現代特性**: 
  - CSS 變數系統
  - Flexbox 和 Grid 佈局
  - 動畫和過渡效果
  - 響應式設計
- **JavaScript ES6+**:
  - 類別和模組化
  - 箭頭函數
  - 解構賦值
  - 模板字符串

### 程式設計概念
- **模組化架構**: 不同功能分離到不同文件
- **事件驅動**: 用戶交互觸發相應功能
- **狀態管理**: 應用程式狀態的統一管理
- **數據持久化**: localStorage 的使用
- **錯誤處理**: 友好的錯誤提示

### 用戶體驗設計
- **直觀的界面**: 清晰的視覺層次
- **即時反饋**: 操作後的即時提示
- **響應式設計**: 適配不同設備
- **無障礙設計**: 語義化標籤和鍵盤導航

## 擴展練習

### 初級練習
1. 修改主題色彩
2. 添加新的激勵訊息
3. 調整界面佈局

### 中級練習
1. 添加投資歷史記錄功能
2. 實現分數趨勢圖表
3. 添加學生頭像功能

### 高級練習
1. 實現多週數據對比
2. 添加投資收益計算
3. 實現數據統計分析

## 常見問題

**Q: 為什麼重新整理頁面後數據還在？**
A: 系統使用瀏覽器的 localStorage 保存數據，數據會持久保存直到手動清除。

**Q: 如何重置所有數據？**
A: 在瀏覽器控制台執行 `dataManager.clearAllData()` 然後重新整理頁面。

**Q: 可以在手機上使用嗎？**
A: 可以，系統採用響應式設計，支援各種設備。

**Q: 如何添加更多學生？**
A: 使用管理員面板的「學生管理」功能，系統已預設26位學生。

**Q: 結算規則是什麼？**
A: 每位學生選擇3位同學投資，如果投資的同學進入前3名，每位可獲得100點獎勵，最高300點。

**Q: 如何查看歷史結算記錄？**
A: 在管理員面板的「歷史記錄」標籤中，選擇週次即可查看詳細的結算結果。

## 技術支援

如遇到問題，請檢查：
1. 瀏覽器控制台是否有錯誤訊息
2. 瀏覽器是否支援 localStorage
3. 所有 CSS 和 JS 文件是否正確載入

建議使用現代瀏覽器：Chrome 60+、Firefox 55+、Safari 12+、Edge 79+
