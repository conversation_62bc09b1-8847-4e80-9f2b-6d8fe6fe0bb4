# 學生答題投資系統 - 測試計劃

## 專案概述
**專案名稱**: 學生答題投資系統  
**測試工程師**: <PERSON> (Augment Agent)  
**測試開始日期**: 2025/8/6  
**測試類型**: 功能測試、UI測試、數據完整性測試

## 測試範圍

### 核心功能模組
1. **學生管理系統**
2. **投資系統**
3. **分數管理系統**
4. **週結算系統**
5. **歷史記錄系統**
6. **數據匯出/匯入系統**
7. **主題切換系統**

### 測試環境
- **瀏覽器**: Chrome 120+, Firefox 120+, Safari 17+, Edge 120+
- **設備**: 桌面電腦、平板、手機
- **解析度**: 1920x1080, 1366x768, 768x1024, 375x667

## 自動化測試架構

### 測試工具選擇
```javascript
// 建議的測試技術棧
{
  "e2e_testing": "Playwright",
  "unit_testing": "Jest",
  "visual_testing": "Percy/Chromatic",
  "performance": "Lighthouse CI",
  "accessibility": "axe-core"
}
```

### 測試數據管理
```javascript
// 測試數據結構
const testData = {
  students: [
    { seat_number: 1, name: "測試學生A" },
    { seat_number: 2, name: "測試學生B" },
    // ... 26位測試學生
  ],
  scores: [
    { student_id: 1, score: 95, week: 1 },
    { student_id: 2, score: 88, week: 1 }
  ],
  investments: [
    { investor_seat: 1, target_seat: 2, week: 1 }
  ]
};
```

## 測試案例設計

### TC001: 學生管理功能測試
**測試目標**: 驗證學生新增、編輯、刪除功能

**前置條件**: 
- 系統已初始化
- 瀏覽器已開啟系統頁面

**測試步驟**:
1. 點擊「管理員」按鈕
2. 切換到「學生管理」標籤
3. 點擊「新增學生」按鈕
4. 輸入座號「27」，姓名「測試學生」
5. 點擊「新增」按鈕

**預期結果**:
- ✅ 學生成功新增到系統
- ✅ 學生列表顯示新學生
- ✅ 座號選擇器包含新學生
- ✅ 顯示成功通知訊息

**測試數據**:
```javascript
const testCase001 = {
  input: {
    seat_number: 27,
    name: "測試學生"
  },
  expected: {
    success: true,
    notification: "學生新增成功",
    studentCount: 27
  }
};
```

### TC002: 投資系統功能測試
**測試目標**: 驗證學生投資流程

**前置條件**:
- 系統有至少3位學生
- 當前為投資階段

**測試步驟**:
1. 選擇投資者座號「1」
2. 選擇投資目標「2, 3, 4」
3. 點擊「確認投資」按鈕
4. 確認投資對話框

**預期結果**:
- ✅ 投資記錄成功保存
- ✅ 排行榜顯示投資者標記
- ✅ 投資者無法重複投資
- ✅ 顯示投資成功通知

### TC003: 週結算功能測試
**測試目標**: 驗證週結算計算邏輯

**前置條件**:
- 系統有完整的分數和投資數據
- 當前週次為可結算狀態

**測試步驟**:
1. 進入管理員面板
2. 切換到「結算系統」
3. 點擊「執行週結算」
4. 確認結算對話框

**預期結果**:
- ✅ 前三名計算正確
- ✅ 投資收益計算正確
- ✅ 歷史記錄正確保存
- ✅ 週次正確遞增

### TC004: 數據完整性測試
**測試目標**: 驗證數據存儲和恢復

**測試步驟**:
1. 創建測試數據
2. 執行數據匯出
3. 清除瀏覽器數據
4. 執行數據匯入
5. 驗證數據完整性

**預期結果**:
- ✅ 所有數據正確匯出
- ✅ 匯入後數據完全一致
- ✅ 功能正常運作

## 自動化測試腳本範例

### Playwright E2E 測試
```javascript
// tests/student-management.spec.js
import { test, expect } from '@playwright/test';

test.describe('學生管理系統', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('file:///path/to/index.html');
    await page.click('[data-testid="admin-button"]');
    await page.click('[data-testid="student-management-tab"]');
  });

  test('TC001: 新增學生功能', async ({ page }) => {
    // 點擊新增學生按鈕
    await page.click('[data-testid="add-student-button"]');
    
    // 填寫學生資料
    await page.fill('[data-testid="seat-number-input"]', '27');
    await page.fill('[data-testid="student-name-input"]', '測試學生');
    
    // 提交表單
    await page.click('[data-testid="submit-button"]');
    
    // 驗證結果
    await expect(page.locator('[data-testid="success-notification"]'))
      .toContainText('學生新增成功');
    
    // 驗證學生列表
    await expect(page.locator('[data-testid="student-list"]'))
      .toContainText('27 測試學生');
  });

  test('TC002: 座號重複驗證', async ({ page }) => {
    // 嘗試新增重複座號
    await page.click('[data-testid="add-student-button"]');
    await page.fill('[data-testid="seat-number-input"]', '1');
    await page.fill('[data-testid="student-name-input"]', '重複學生');
    await page.click('[data-testid="submit-button"]');
    
    // 驗證錯誤訊息
    await expect(page.locator('[data-testid="error-notification"]'))
      .toContainText('座號已存在');
  });
});
```

### Jest 單元測試
```javascript
// tests/data-manager.test.js
import { DataManager } from '../js/data.js';

describe('DataManager', () => {
  let dataManager;
  
  beforeEach(() => {
    dataManager = new DataManager();
    localStorage.clear();
  });

  test('應該正確新增學生', () => {
    const student = {
      seat_number: 27,
      name: '測試學生'
    };
    
    const result = dataManager.addStudent(student);
    
    expect(result.success).toBe(true);
    expect(result.student.seat_number).toBe(27);
    expect(result.student.name).toBe('測試學生');
  });

  test('應該拒絕重複座號', () => {
    // 先新增一個學生
    dataManager.addStudent({ seat_number: 1, name: '學生A' });
    
    // 嘗試新增相同座號
    const result = dataManager.addStudent({ seat_number: 1, name: '學生B' });
    
    expect(result.success).toBe(false);
    expect(result.error).toContain('座號已存在');
  });
});
```

## 測試報告範例

### 測試執行摘要
**測試日期**: 2025/8/6  
**測試版本**: v1.2.0  
**測試環境**: Chrome 120.0.6099.109

| 測試類型 | 總數 | 通過 | 失敗 | 跳過 | 通過率 |
|---------|------|------|------|------|--------|
| 功能測試 | 45   | 43   | 2    | 0    | 95.6%  |
| UI測試   | 28   | 28   | 0    | 0    | 100%   |
| 數據測試 | 15   | 14   | 1    | 0    | 93.3%  |
| **總計** | **88** | **85** | **3** | **0** | **96.6%** |

### 缺陷報告

#### Bug #001: 投資統計計算錯誤
**嚴重程度**: 高  
**優先級**: P1  
**發現日期**: 2025/8/6

**問題描述**:
在特定情況下，學生投資統計的成功率計算不正確。當學生投資3次，成功2次時，顯示為66.6%而非66.7%。

**重現步驟**:
1. 學生投資3次
2. 其中2次成功，1次失敗
3. 查看投資統計表格

**預期結果**: 成功率顯示66.7%  
**實際結果**: 成功率顯示66.6%

**修復建議**:
```javascript
// 修復前
const successRate = (successfulBets / totalBets * 100).toFixed(1);

// 修復後  
const successRate = Math.round(successfulBets / totalBets * 1000) / 10;
```

#### Bug #002: 手機版下載按鈕位置異常
**嚴重程度**: 中  
**優先級**: P2

**問題描述**:
在手機版瀏覽器中，下載按鈕與其他按鈕重疊。

**修復建議**:
調整CSS媒體查詢，增加按鈕間距。

## 測試覆蓋率策略

### 功能覆蓋率目標
- **核心功能**: 100%
- **次要功能**: 95%
- **邊界情況**: 90%
- **錯誤處理**: 95%

### 代碼覆蓋率目標
- **語句覆蓋率**: ≥90%
- **分支覆蓋率**: ≥85%
- **函數覆蓋率**: ≥95%

### 測試數據覆蓋
- **正常數據**: 100%
- **邊界數據**: 100%
- **異常數據**: 90%
- **空值處理**: 100%

## 持續集成配置

### GitHub Actions 工作流程
```yaml
# .github/workflows/test.yml
name: 自動化測試

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: 安裝依賴
        run: npm install
      
      - name: 執行單元測試
        run: npm run test:unit
      
      - name: 執行E2E測試
        run: npm run test:e2e
      
      - name: 生成測試報告
        run: npm run test:report
```

## 測試品質保證

### 測試審查清單
- [ ] 測試案例覆蓋所有需求
- [ ] 測試數據具有代表性
- [ ] 斷言明確且完整
- [ ] 測試獨立且可重複
- [ ] 錯誤訊息清晰易懂

### 測試維護策略
1. **定期更新**: 每週檢查測試案例
2. **失敗分析**: 立即分析失敗原因
3. **覆蓋率監控**: 持續監控覆蓋率變化
4. **性能測試**: 定期執行性能基準測試

---

**測試負責人**: Claude AI (Augment Agent)  
**最後更新**: 2025/8/6  
**下次審查**: 2025/8/13
