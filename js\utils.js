/**
 * 工具函數模組
 * 提供各種實用的輔助函數
 */

/**
 * DOM 操作工具
 */
const DOM = {
    /**
     * 獲取元素
     */
    get: (selector) => document.querySelector(selector),
    getAll: (selector) => document.querySelectorAll(selector),
    
    /**
     * 創建元素
     */
    create: (tag, className = '', innerHTML = '') => {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    },
    
    /**
     * 顯示/隱藏元素
     */
    show: (element) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (element) element.style.display = '';
    },
    
    hide: (element) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (element) element.style.display = 'none';
    },
    
    toggle: (element) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (element) {
            element.style.display = element.style.display === 'none' ? '' : 'none';
        }
    },
    
    /**
     * 添加/移除類名
     */
    addClass: (element, className) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (element) element.classList.add(className);
    },
    
    removeClass: (element, className) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (element) element.classList.remove(className);
    },
    
    toggleClass: (element, className) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (element) element.classList.toggle(className);
    },
    
    hasClass: (element, className) => {
        if (typeof element === 'string') element = DOM.get(element);
        return element ? element.classList.contains(className) : false;
    }
};

/**
 * 動畫工具
 */
const Animation = {
    /**
     * 淡入效果
     */
    fadeIn: (element, duration = 300) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (!element) return;
        
        element.style.opacity = '0';
        element.style.display = '';
        
        let start = null;
        const animate = (timestamp) => {
            if (!start) start = timestamp;
            const progress = timestamp - start;
            const opacity = Math.min(progress / duration, 1);
            
            element.style.opacity = opacity;
            
            if (progress < duration) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    },
    
    /**
     * 淡出效果
     */
    fadeOut: (element, duration = 300) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (!element) return;
        
        let start = null;
        const animate = (timestamp) => {
            if (!start) start = timestamp;
            const progress = timestamp - start;
            const opacity = Math.max(1 - progress / duration, 0);
            
            element.style.opacity = opacity;
            
            if (progress < duration) {
                requestAnimationFrame(animate);
            } else {
                element.style.display = 'none';
            }
        };
        
        requestAnimationFrame(animate);
    },
    
    /**
     * 滑入效果
     */
    slideIn: (element, direction = 'down', duration = 300) => {
        if (typeof element === 'string') element = DOM.get(element);
        if (!element) return;
        
        const transforms = {
            down: 'translateY(-20px)',
            up: 'translateY(20px)',
            left: 'translateX(20px)',
            right: 'translateX(-20px)'
        };
        
        element.style.transform = transforms[direction];
        element.style.opacity = '0';
        element.style.display = '';
        
        setTimeout(() => {
            element.style.transition = `all ${duration}ms ease-out`;
            element.style.transform = 'translate(0)';
            element.style.opacity = '1';
        }, 10);
    }
};

/**
 * 通知工具
 */
const Notification = {
    /**
     * 顯示通知
     */
    show: (message, type = 'info', duration = 3000) => {
        const notification = DOM.create('div', `notification notification-${type}`);
        notification.innerHTML = `
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        `;
        
        // 添加樣式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-white);
            border: 2px solid var(--border-light);
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 1rem;
            max-width: 400px;
            transform: translateX(100%);
            transition: transform 0.3s ease-out;
        `;
        
        // 設置類型顏色
        const colors = {
            success: 'var(--success)',
            error: 'var(--error)',
            warning: 'var(--warning)',
            info: 'var(--info)'
        };
        
        if (colors[type]) {
            notification.style.borderColor = colors[type];
        }
        
        document.body.appendChild(notification);
        
        // 滑入動畫
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // 關閉按鈕事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-light);
            padding: 0;
            margin-left: auto;
        `;
        
        const close = () => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        };
        
        closeBtn.addEventListener('click', close);
        
        // 自動關閉
        if (duration > 0) {
            setTimeout(close, duration);
        }
    },
    
    success: (message, duration) => Notification.show(message, 'success', duration),
    error: (message, duration) => Notification.show(message, 'error', duration),
    warning: (message, duration) => Notification.show(message, 'warning', duration),
    info: (message, duration) => Notification.show(message, 'info', duration)
};

/**
 * 表單驗證工具
 */
const Validator = {
    /**
     * 驗證必填欄位
     */
    required: (value) => {
        return value !== null && value !== undefined && value.toString().trim() !== '';
    },
    
    /**
     * 驗證數字
     */
    isNumber: (value) => {
        return !isNaN(value) && !isNaN(parseFloat(value));
    },
    
    /**
     * 驗證範圍
     */
    inRange: (value, min, max) => {
        const num = parseFloat(value);
        return num >= min && num <= max;
    },
    
    /**
     * 驗證座號
     */
    validSeatNumber: (seatNumber) => {
        return Validator.isNumber(seatNumber) && 
               Validator.inRange(seatNumber, 1, 50) && 
               Number.isInteger(parseFloat(seatNumber));
    },
    
    /**
     * 驗證學生姓名
     */
    validStudentName: (name) => {
        return Validator.required(name) && name.trim().length >= 2 && name.trim().length <= 10;
    }
};

/**
 * 格式化工具
 */
const Format = {
    /**
     * 格式化日期
     */
    date: (date, format = 'YYYY-MM-DD HH:mm:ss') => {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 格式化數字
     */
    number: (num, decimals = 0) => {
        return parseFloat(num).toFixed(decimals);
    },

    /**
     * 檢測檔案格式
     */
    detectFileFormat: (fileName) => {
        const extension = fileName.toLowerCase().split('.').pop();
        switch (extension) {
            case 'json':
                return 'json';
            case 'csv':
                return 'csv';
            case 'xlsx':
            case 'xls':
                return 'excel';
            default:
                return 'unknown';
        }
    },

    /**
     * 獲取MIME類型
     */
    getMimeType: (format) => {
        switch (format.toLowerCase()) {
            case 'json':
                return 'application/json';
            case 'csv':
                return 'text/csv';
            case 'excel':
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            default:
                return 'text/plain';
        }
    },

    /**
     * 獲取檔案副檔名
     */
    getFileExtension: (format) => {
        switch (format.toLowerCase()) {
            case 'json':
                return 'json';
            case 'csv':
                return 'csv';
            case 'excel':
                return 'xlsx';
            default:
                return 'txt';
        }
    }
};

/**
 * 隨機工具
 */
const Random = {
    /**
     * 生成隨機整數
     */
    int: (min, max) => {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    /**
     * 從陣列中隨機選擇
     */
    choice: (array) => {
        return array[Random.int(0, array.length - 1)];
    },
    
    /**
     * 生成隨機 ID
     */
    id: () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
};

/**
 * 激勵訊息生成器
 */
const MotivationMessages = {
    general: [
        "加油！你一定可以的！💪",
        "相信自己，你很棒！✨",
        "每一次努力都會有回報！🌟",
        "堅持下去，成功就在前方！🎯",
        "你的努力大家都看得見！👏"
    ],
    
    highScore: [
        "哇！你的分數真高！繼續保持！🏆",
        "太厲害了！你是我們的榜樣！⭐",
        "分數這麼高，一定很努力！👑",
        "你真的很優秀！大家都佩服你！🎉"
    ],
    
    improvement: [
        "進步了！繼續加油！📈",
        "看到你的努力了！很棒！💫",
        "一步一步往前走，你做得很好！🚀",
        "進步就是最大的成功！🌈"
    ],
    
    encouragement: [
        "沒關係，下次一定會更好！🌸",
        "每個人都有自己的節奏，慢慢來！🌱",
        "相信自己，你有無限可能！💝",
        "失敗是成功之母，加油！🌺"
    ],
    
    getRandom: (category = 'general') => {
        const messages = MotivationMessages[category] || MotivationMessages.general;
        return Random.choice(messages);
    }
};

/**
 * 浮動星星效果
 */
const FloatingStars = {
    stars: ['⭐', '✨', '🌟', '💫', '⚡', '🎈', '🎀', '💖'],
    
    create: () => {
        const container = DOM.get('#floatingStars');
        if (!container) return;
        
        // 清除現有星星
        container.innerHTML = '';
        
        // 創建新星星
        for (let i = 0; i < 15; i++) {
            const star = DOM.create('div', 'floating-star');
            star.innerHTML = Random.choice(FloatingStars.stars);
            star.style.left = Random.int(0, 100) + '%';
            star.style.top = Random.int(0, 100) + '%';
            star.style.animationDelay = Random.int(0, 6) + 's';
            star.style.animationDuration = Random.int(4, 8) + 's';
            container.appendChild(star);
        }
    }
};

/**
 * 防抖函數
 */
const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

/**
 * 節流函數
 */
const throttle = (func, limit) => {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

// 將工具函數掛載到全局
window.DOM = DOM;
window.Animation = Animation;
window.Notification = Notification;
window.Validator = Validator;
window.Format = Format;
window.Random = Random;
window.MotivationMessages = MotivationMessages;
window.FloatingStars = FloatingStars;
window.debounce = debounce;
window.throttle = throttle;
