/**
 * 數據管理模組
 * 使用 localStorage 替代 SQLite 數據庫
 */

class DataManager {
    constructor() {
        this.storageKeys = {
            students: 'betting_system_students',
            scores: 'betting_system_scores',
            bets: 'betting_system_bets',
            currentWeek: 'betting_system_current_week',
            settings: 'betting_system_settings',
            rewards: 'betting_system_rewards'
        };
        
        this.initializeData();
    }

    /**
     * 初始化數據
     */
    initializeData() {
        // 如果是第一次使用，創建示例數據
        if (!this.getStudents().length) {
            this.createSampleData();
        }
        
        // 確保當前週數存在
        if (!this.getCurrentWeek()) {
            this.setCurrentWeek(1);
        }
    }

    /**
     * 創建示例數據
     */
    createSampleData() {
        const sampleStudents = [
            { seat_number: 1, name: '王小明' },
            { seat_number: 2, name: '李小華' },
            { seat_number: 3, name: '張小美' },
            { seat_number: 4, name: '陳小強' },
            { seat_number: 5, name: '林小雅' },
            { seat_number: 6, name: '黃小偉' },
            { seat_number: 7, name: '吳小芳' },
            { seat_number: 8, name: '劉小傑' },
            { seat_number: 9, name: '蔡小玲' },
            { seat_number: 10, name: '許小宇' },
            { seat_number: 11, name: '趙小文' },
            { seat_number: 12, name: '錢小安' },
            { seat_number: 13, name: '孫小樂' },
            { seat_number: 14, name: '李小慧' },
            { seat_number: 15, name: '周小勇' },
            { seat_number: 16, name: '吳小靜' },
            { seat_number: 17, name: '鄭小峰' },
            { seat_number: 18, name: '王小麗' },
            { seat_number: 19, name: '馮小軍' },
            { seat_number: 20, name: '陳小敏' },
            { seat_number: 21, name: '褚小龍' },
            { seat_number: 22, name: '衛小鳳' },
            { seat_number: 23, name: '蔣小濤' },
            { seat_number: 24, name: '沈小霞' },
            { seat_number: 25, name: '韓小剛' },
            { seat_number: 26, name: '楊小梅' }
        ];

        // 添加學生
        sampleStudents.forEach(student => {
            this.addStudent(student.seat_number, student.name);
        });

        // 創建示例分數
        const sampleScores = [
            { seat_number: 1, score: 85 },
            { seat_number: 2, score: 92 },
            { seat_number: 3, score: 78 },
            { seat_number: 4, score: 88 },
            { seat_number: 5, score: 95 },
            { seat_number: 6, score: 82 },
            { seat_number: 7, score: 90 },
            { seat_number: 8, score: 76 },
            { seat_number: 9, score: 89 },
            { seat_number: 10, score: 84 },
            { seat_number: 11, score: 91 },
            { seat_number: 12, score: 87 },
            { seat_number: 13, score: 93 },
            { seat_number: 14, score: 79 },
            { seat_number: 15, score: 86 },
            { seat_number: 16, score: 94 },
            { seat_number: 17, score: 81 },
            { seat_number: 18, score: 88 },
            { seat_number: 19, score: 77 },
            { seat_number: 20, score: 92 },
            { seat_number: 21, score: 83 },
            { seat_number: 22, score: 89 },
            { seat_number: 23, score: 96 },
            { seat_number: 24, score: 80 },
            { seat_number: 25, score: 85 },
            { seat_number: 26, score: 91 }
        ];

        sampleScores.forEach(score => {
            this.updateScore(score.seat_number, score.score);
        });
    }

    /**
     * 獲取本地存儲數據
     */
    getStorageData(key) {
        try {
            const data = localStorage.getItem(this.storageKeys[key]);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`獲取 ${key} 數據失敗:`, error);
            return [];
        }
    }

    /**
     * 設置本地存儲數據
     */
    setStorageData(key, data) {
        try {
            localStorage.setItem(this.storageKeys[key], JSON.stringify(data));
            return true;
        } catch (error) {
            console.error(`保存 ${key} 數據失敗:`, error);
            return false;
        }
    }

    /**
     * 學生管理
     */
    getStudents() {
        return this.getStorageData('students').sort((a, b) => a.seat_number - b.seat_number);
    }

    addStudent(seatNumber, name) {
        const students = this.getStudents();
        
        // 檢查座號是否已存在
        if (students.find(s => s.seat_number === seatNumber)) {
            throw new Error('座號已存在');
        }

        const newStudent = {
            id: Date.now(),
            seat_number: seatNumber,
            name: name,
            created_at: new Date().toISOString()
        };

        students.push(newStudent);
        this.setStorageData('students', students);

        // 為新學生初始化本週分數
        this.initializeStudentScore(seatNumber);

        return newStudent;
    }

    updateStudent(id, name) {
        const students = this.getStudents();
        const studentIndex = students.findIndex(s => s.id === id);
        
        if (studentIndex === -1) {
            throw new Error('學生不存在');
        }

        students[studentIndex].name = name;
        students[studentIndex].updated_at = new Date().toISOString();
        
        return this.setStorageData('students', students);
    }

    deleteStudent(id) {
        const students = this.getStudents();
        const student = students.find(s => s.id === id);
        
        if (!student) {
            throw new Error('學生不存在');
        }

        // 刪除學生
        const filteredStudents = students.filter(s => s.id !== id);
        this.setStorageData('students', filteredStudents);

        // 刪除相關的分數和投注記錄
        this.deleteStudentScores(student.seat_number);
        this.deleteStudentBets(student.seat_number);

        return true;
    }

    getStudentBySeat(seatNumber) {
        return this.getStudents().find(s => s.seat_number === seatNumber);
    }

    /**
     * 分數管理
     */
    getScores(week = null) {
        const currentWeek = week || this.getCurrentWeek();
        const scores = this.getStorageData('scores');
        const students = this.getStudents();
        
        // 獲取指定週的分數，並與學生信息合併
        const weekScores = scores
            .filter(s => s.week === currentWeek)
            .map(score => {
                const student = students.find(s => s.seat_number === score.seat_number);
                return {
                    ...score,
                    name: student ? student.name : '未知學生'
                };
            })
            .sort((a, b) => b.score - a.score); // 按分數降序排列

        return weekScores;
    }

    updateScore(seatNumber, score, week = null) {
        const currentWeek = week || this.getCurrentWeek();
        const scores = this.getStorageData('scores');
        
        // 查找現有分數記錄
        const existingIndex = scores.findIndex(s => 
            s.seat_number === seatNumber && s.week === currentWeek
        );

        const scoreData = {
            seat_number: seatNumber,
            score: score,
            week: currentWeek,
            updated_at: new Date().toISOString()
        };

        if (existingIndex >= 0) {
            // 更新現有記錄
            scores[existingIndex] = { ...scores[existingIndex], ...scoreData };
        } else {
            // 創建新記錄
            scores.push({
                id: Date.now(),
                ...scoreData,
                created_at: new Date().toISOString()
            });
        }

        return this.setStorageData('scores', scores);
    }

    initializeStudentScore(seatNumber, week = null) {
        const currentWeek = week || this.getCurrentWeek();
        const scores = this.getStorageData('scores');
        
        // 檢查是否已有分數記錄
        const existing = scores.find(s => 
            s.seat_number === seatNumber && s.week === currentWeek
        );

        if (!existing) {
            this.updateScore(seatNumber, 0, currentWeek);
        }
    }

    deleteStudentScores(seatNumber) {
        const scores = this.getStorageData('scores');
        const filteredScores = scores.filter(s => s.seat_number !== seatNumber);
        return this.setStorageData('scores', filteredScores);
    }

    /**
     * 投注管理
     */
    getBets(week = null) {
        const currentWeek = week || this.getCurrentWeek();
        return this.getStorageData('bets').filter(b => b.week === currentWeek);
    }

    submitBets(bettorSeatNumber, bets, week = null) {
        const currentWeek = week || this.getCurrentWeek();
        const allBets = this.getStorageData('bets');
        
        // 檢查本週是否已經投注
        const existingBets = allBets.filter(b => 
            b.bettor_seat_number === bettorSeatNumber && b.week === currentWeek
        );

        if (existingBets.length > 0) {
            throw new Error('本週已經投注過了');
        }

        // 驗證投注數據
        if (!Array.isArray(bets) || bets.length !== 3) {
            throw new Error('必須選擇剛好3位同學投注');
        }

        // 檢查不能對自己投注
        const selfBet = bets.find(bet => bet.targetSeatNumber === bettorSeatNumber);
        if (selfBet) {
            throw new Error('不能對自己投注');
        }

        // 添加投注記錄
        const newBets = bets.map(bet => ({
            id: Date.now() + Math.random(),
            bettor_seat_number: bettorSeatNumber,
            target_seat_number: bet.targetSeatNumber,
            amount: bet.amount || 1,
            week: currentWeek,
            created_at: new Date().toISOString()
        }));

        allBets.push(...newBets);
        return this.setStorageData('bets', allBets);
    }

    getBetsOnStudent(targetSeatNumber, week = null) {
        const currentWeek = week || this.getCurrentWeek();
        const bets = this.getBets(currentWeek);
        const students = this.getStudents();
        
        return bets
            .filter(b => b.target_seat_number === targetSeatNumber)
            .map(bet => {
                const bettor = students.find(s => s.seat_number === bet.bettor_seat_number);
                return {
                    ...bet,
                    bettor_name: bettor ? bettor.name : '未知學生'
                };
            });
    }

    getBetsByBettor(bettorSeatNumber, week = null) {
        const currentWeek = week || this.getCurrentWeek();
        const bets = this.getBets(currentWeek);
        const students = this.getStudents();
        
        return bets
            .filter(b => b.bettor_seat_number === bettorSeatNumber)
            .map(bet => {
                const target = students.find(s => s.seat_number === bet.target_seat_number);
                return {
                    ...bet,
                    target_name: target ? target.name : '未知學生'
                };
            });
    }

    deleteStudentBets(seatNumber) {
        const bets = this.getStorageData('bets');
        const filteredBets = bets.filter(b => 
            b.bettor_seat_number !== seatNumber && b.target_seat_number !== seatNumber
        );
        return this.setStorageData('bets', filteredBets);
    }

    /**
     * 週管理
     */
    getCurrentWeek() {
        return parseInt(localStorage.getItem(this.storageKeys.currentWeek)) || 1;
    }

    setCurrentWeek(week) {
        localStorage.setItem(this.storageKeys.currentWeek, week.toString());
    }

    /**
     * 週結算
     */
    performWeeklySettlement() {
        const currentWeek = this.getCurrentWeek();
        const nextWeek = currentWeek + 1;

        // 計算投資收益
        this.calculateInvestmentRewards(currentWeek);

        // 保存結算記錄
        this.saveSettlementRecord(currentWeek);

        // 開始新的一週
        this.setCurrentWeek(nextWeek);

        // 為所有學生初始化新週分數
        const students = this.getStudents();
        students.forEach(student => {
            this.initializeStudentScore(student.seat_number, nextWeek);
        });

        return nextWeek;
    }

    /**
     * 計算投資收益
     */
    calculateInvestmentRewards(week) {
        const scores = this.getScores(week);
        const bets = this.getBets(week);
        const students = this.getStudents();

        // 獲取前3名
        const top3 = scores.slice(0, 3).map(s => s.seat_number);

        // 計算每個投資者的收益
        const investorRewards = {};

        bets.forEach(bet => {
            if (!investorRewards[bet.bettor_seat_number]) {
                investorRewards[bet.bettor_seat_number] = {
                    totalReward: 0,
                    successfulBets: [],
                    allBets: []
                };
            }

            const reward = {
                targetSeat: bet.target_seat_number,
                targetName: students.find(s => s.seat_number === bet.target_seat_number)?.name || '未知',
                isWin: top3.includes(bet.target_seat_number),
                reward: top3.includes(bet.target_seat_number) ? 100 : 0
            };

            investorRewards[bet.bettor_seat_number].allBets.push(reward);

            if (reward.isWin) {
                investorRewards[bet.bettor_seat_number].totalReward += 100;
                investorRewards[bet.bettor_seat_number].successfulBets.push(reward);
            }
        });

        // 將獎勵加到投資者的分數上
        Object.keys(investorRewards).forEach(investorSeat => {
            const reward = investorRewards[investorSeat];
            if (reward.totalReward > 0) {
                const currentScore = scores.find(s => s.seat_number === parseInt(investorSeat))?.score || 0;
                this.updateScore(parseInt(investorSeat), currentScore + reward.totalReward, week);
            }
        });

        // 計算每位學生的投資統計
        const studentInvestmentStats = {};
        students.forEach(student => {
            const studentBets = bets.filter(bet => bet.bettor_seat_number === student.seat_number);
            const successfulBets = studentBets.filter(bet => top3.includes(bet.target_seat_number));

            studentInvestmentStats[student.seat_number] = {
                name: student.name,
                totalBets: studentBets.length,
                successfulBets: successfulBets.length,
                totalReward: successfulBets.length * 100,
                betDetails: studentBets.map(bet => ({
                    targetSeat: bet.target_seat_number,
                    targetName: students.find(s => s.seat_number === bet.target_seat_number)?.name || '未知',
                    isWin: top3.includes(bet.target_seat_number),
                    reward: top3.includes(bet.target_seat_number) ? 100 : 0
                }))
            };
        });

        // 保存收益記錄
        const rewards = this.getStorageData('rewards') || [];
        rewards.push({
            week: week,
            top3: top3.map(seat => ({
                seat_number: seat,
                name: students.find(s => s.seat_number === seat)?.name || '未知',
                score: scores.find(s => s.seat_number === seat)?.score || 0
            })),
            investorRewards: investorRewards,
            studentInvestmentStats: studentInvestmentStats,
            settlementDate: new Date().toISOString()
        });

        this.setStorageData('rewards', rewards);

        return investorRewards;
    }

    /**
     * 保存結算記錄
     */
    saveSettlementRecord(week) {
        const settlements = this.getStorageData('settlements') || [];
        const scores = this.getScores(week);
        const bets = this.getBets(week);

        settlements.push({
            week: week,
            scores: scores,
            bets: bets,
            settlementDate: new Date().toISOString()
        });

        this.setStorageData('settlements', settlements);
    }

    /**
     * 獲取結算記錄
     */
    getSettlementRecords() {
        return this.getStorageData('settlements') || [];
    }

    /**
     * 獲取收益記錄
     */
    getRewardRecords() {
        return this.getStorageData('rewards') || [];
    }

    /**
     * 獲取特定週的結算結果
     */
    getWeekSettlement(week) {
        const rewards = this.getRewardRecords();
        return rewards.find(r => r.week === week);
    }

    /**
     * 數據導出/導入
     */
    exportData(format = 'json') {
        const data = {
            students: this.getStorageData('students'),
            scores: this.getStorageData('scores'),
            bets: this.getStorageData('bets'),
            currentWeek: this.getCurrentWeek(),
            exportDate: new Date().toISOString()
        };

        switch (format.toLowerCase()) {
            case 'json':
                return this.exportToJSON(data);
            case 'csv':
                return this.exportToCSV(data);
            case 'excel':
                return this.exportToExcel(data);
            default:
                return this.exportToJSON(data);
        }
    }

    exportToJSON(data) {
        return JSON.stringify(data, null, 2);
    }

    exportToCSV(data) {
        let csv = '';

        // 學生資料
        csv += '=== 學生資料 ===\n';
        csv += 'ID,座號,姓名,創建時間\n';
        data.students.forEach(student => {
            csv += `${student.id},${student.seat_number},"${student.name}",${student.created_at}\n`;
        });

        csv += '\n=== 分數記錄 ===\n';
        csv += 'ID,座號,分數,週次,創建時間\n';
        data.scores.forEach(score => {
            csv += `${score.id},${score.seat_number},${score.score},${score.week},${score.created_at}\n`;
        });

        csv += '\n=== 投資記錄 ===\n';
        csv += 'ID,投資者座號,目標座號,金額,週次,創建時間\n';
        data.bets.forEach(bet => {
            csv += `${bet.id},${bet.bettor_seat_number},${bet.target_seat_number},${bet.amount},${bet.week},${bet.created_at}\n`;
        });

        csv += `\n=== 系統資訊 ===\n`;
        csv += `當前週次,${data.currentWeek}\n`;
        csv += `匯出時間,${data.exportDate}\n`;

        return csv;
    }

    exportToExcel(data) {
        // 創建工作簿
        const workbook = {
            SheetNames: ['學生資料', '分數記錄', '投資記錄', '系統資訊'],
            Sheets: {}
        };

        // 學生資料工作表
        const studentsData = [
            ['ID', '座號', '姓名', '創建時間'],
            ...data.students.map(s => [s.id, s.seat_number, s.name, s.created_at])
        ];
        workbook.Sheets['學生資料'] = this.arrayToSheet(studentsData);

        // 分數記錄工作表
        const scoresData = [
            ['ID', '座號', '分數', '週次', '創建時間'],
            ...data.scores.map(s => [s.id, s.seat_number, s.score, s.week, s.created_at])
        ];
        workbook.Sheets['分數記錄'] = this.arrayToSheet(scoresData);

        // 投資記錄工作表
        const betsData = [
            ['ID', '投資者座號', '目標座號', '金額', '週次', '創建時間'],
            ...data.bets.map(b => [b.id, b.bettor_seat_number, b.target_seat_number, b.amount, b.week, b.created_at])
        ];
        workbook.Sheets['投資記錄'] = this.arrayToSheet(betsData);

        // 系統資訊工作表
        const systemData = [
            ['項目', '值'],
            ['當前週次', data.currentWeek],
            ['匯出時間', data.exportDate]
        ];
        workbook.Sheets['系統資訊'] = this.arrayToSheet(systemData);

        return this.workbookToExcel(workbook);
    }

    arrayToSheet(data) {
        const sheet = {};
        const range = { s: { c: 0, r: 0 }, e: { c: 0, r: 0 } };

        for (let R = 0; R < data.length; R++) {
            for (let C = 0; C < data[R].length; C++) {
                if (range.s.r > R) range.s.r = R;
                if (range.s.c > C) range.s.c = C;
                if (range.e.r < R) range.e.r = R;
                if (range.e.c < C) range.e.c = C;

                const cell = { v: data[R][C] };
                if (cell.v == null) continue;

                const cellRef = this.encodeCellAddress({ c: C, r: R });

                if (typeof cell.v === 'number') cell.t = 'n';
                else if (typeof cell.v === 'boolean') cell.t = 'b';
                else cell.t = 's';

                sheet[cellRef] = cell;
            }
        }

        if (range.s.c < 10000000) sheet['!ref'] = this.encodeRange(range);
        return sheet;
    }

    encodeCellAddress(cell) {
        return String.fromCharCode(65 + cell.c) + (cell.r + 1);
    }

    encodeRange(range) {
        return this.encodeCellAddress(range.s) + ':' + this.encodeCellAddress(range.e);
    }

    workbookToExcel(workbook) {
        // 簡化的Excel格式輸出（實際應用中建議使用SheetJS庫）
        let excel = 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,';

        // 這裡返回一個簡化的XML格式，實際使用時建議整合SheetJS
        const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
    <sheets>
        ${workbook.SheetNames.map((name, index) =>
            `<sheet name="${name}" sheetId="${index + 1}"/>`
        ).join('')}
    </sheets>
</workbook>`;

        return excel + btoa(xmlContent);
    }

    importData(fileContent, format = 'json') {
        try {
            let data;

            switch (format.toLowerCase()) {
                case 'json':
                    data = this.importFromJSON(fileContent);
                    break;
                case 'csv':
                    data = this.importFromCSV(fileContent);
                    break;
                case 'excel':
                    data = this.importFromExcel(fileContent);
                    break;
                default:
                    data = this.importFromJSON(fileContent);
            }

            if (data && this.validateImportData(data)) {
                if (data.students) this.setStorageData('students', data.students);
                if (data.scores) this.setStorageData('scores', data.scores);
                if (data.bets) this.setStorageData('bets', data.bets);
                if (data.currentWeek) this.setCurrentWeek(data.currentWeek);

                return true;
            }

            return false;
        } catch (error) {
            console.error('導入數據失敗:', error);
            return false;
        }
    }

    importFromJSON(jsonData) {
        return JSON.parse(jsonData);
    }

    importFromCSV(csvData) {
        const lines = csvData.split('\n');
        const data = { students: [], scores: [], bets: [], currentWeek: 1 };

        let currentSection = '';
        let isDataRow = false;

        for (let line of lines) {
            line = line.trim();
            if (!line) continue;

            if (line.startsWith('=== ') && line.endsWith(' ===')) {
                currentSection = line.replace(/=== | ===/g, '');
                isDataRow = false;
                continue;
            }

            if (!isDataRow) {
                isDataRow = true; // 跳過標題行
                continue;
            }

            const values = this.parseCSVLine(line);

            switch (currentSection) {
                case '學生資料':
                    if (values.length >= 4) {
                        data.students.push({
                            id: parseInt(values[0]),
                            seat_number: parseInt(values[1]),
                            name: values[2].replace(/"/g, ''),
                            created_at: values[3]
                        });
                    }
                    break;

                case '分數記錄':
                    if (values.length >= 5) {
                        data.scores.push({
                            id: parseInt(values[0]),
                            seat_number: parseInt(values[1]),
                            score: parseInt(values[2]),
                            week: parseInt(values[3]),
                            created_at: values[4]
                        });
                    }
                    break;

                case '投資記錄':
                    if (values.length >= 6) {
                        data.bets.push({
                            id: parseInt(values[0]),
                            bettor_seat_number: parseInt(values[1]),
                            target_seat_number: parseInt(values[2]),
                            amount: parseInt(values[3]),
                            week: parseInt(values[4]),
                            created_at: values[5]
                        });
                    }
                    break;

                case '系統資訊':
                    if (values.length >= 2 && values[0] === '當前週次') {
                        data.currentWeek = parseInt(values[1]);
                    }
                    break;
            }
        }

        return data;
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];

            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current);
                current = '';
            } else {
                current += char;
            }
        }

        result.push(current);
        return result;
    }

    importFromExcel(excelData) {
        // 簡化的Excel導入（實際應用中建議使用SheetJS庫）
        // 這裡假設Excel數據已經被轉換為JSON格式
        try {
            // 如果是base64編碼的Excel文件
            if (excelData.startsWith('data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,')) {
                const base64Data = excelData.split(',')[1];
                const xmlContent = atob(base64Data);

                // 簡化處理，實際應該解析Excel XML結構
                // 這裡返回一個默認結構
                return {
                    students: [],
                    scores: [],
                    bets: [],
                    currentWeek: 1
                };
            }

            // 如果是JSON格式的Excel數據
            return JSON.parse(excelData);
        } catch (error) {
            console.error('Excel導入失敗:', error);
            return null;
        }
    }

    validateImportData(data) {
        // 驗證導入數據的完整性
        if (!data || typeof data !== 'object') return false;

        // 檢查必要的數據結構
        if (data.students && !Array.isArray(data.students)) return false;
        if (data.scores && !Array.isArray(data.scores)) return false;
        if (data.bets && !Array.isArray(data.bets)) return false;

        // 驗證學生數據
        if (data.students) {
            for (let student of data.students) {
                if (!student.id || !student.seat_number || !student.name) return false;
            }
        }

        return true;
    }

    /**
     * 清除所有數據
     */
    clearAllData() {
        Object.values(this.storageKeys).forEach(key => {
            localStorage.removeItem(key);
        });
        this.initializeData();
    }

    /**
     * 獲取歷史記錄
     */
    getRewards() {
        return this.getStorageData('rewards') || [];
    }

    /**
     * 下載週結算歷史報告
     */
    downloadWeekHistory(week, format = 'json') {
        const rewards = this.getRewards();
        const weekData = rewards.find(r => r.week === week);

        if (!weekData) {
            throw new Error('找不到該週的結算記錄');
        }

        const reportData = {
            week: week,
            settlementDate: weekData.settlementDate,
            top3: weekData.top3,
            investorRewards: weekData.investorRewards,
            studentInvestmentStats: weekData.studentInvestmentStats,
            summary: this.generateWeekSummary(weekData)
        };

        switch (format.toLowerCase()) {
            case 'json':
                return this.generateJSONReport(reportData);
            case 'csv':
                return this.generateCSVReport(reportData);
            case 'excel':
                return this.generateExcelReport(reportData);
            default:
                return this.generateJSONReport(reportData);
        }
    }

    generateWeekSummary(weekData) {
        const totalInvestors = Object.keys(weekData.investorRewards || {}).length;
        const totalRewards = Object.values(weekData.investorRewards || {})
            .reduce((sum, reward) => sum + reward.totalReward, 0);
        const successfulInvestors = Object.values(weekData.investorRewards || {})
            .filter(reward => reward.totalReward > 0).length;

        return {
            totalInvestors,
            totalRewards,
            successfulInvestors,
            successRate: totalInvestors > 0 ? (successfulInvestors / totalInvestors * 100).toFixed(1) : 0
        };
    }

    generateJSONReport(reportData) {
        return JSON.stringify(reportData, null, 2);
    }

    generateCSVReport(reportData) {
        let csv = `第${reportData.week}週結算報告\n`;
        csv += `結算時間,${reportData.settlementDate}\n\n`;

        // 前三名
        csv += '前三名\n';
        csv += '排名,座號,姓名,分數\n';
        reportData.top3.forEach((student, index) => {
            csv += `${index + 1},${student.seat_number},"${student.name}",${student.score}\n`;
        });

        // 投資統計
        csv += '\n學生投資統計\n';
        csv += '座號,姓名,投資次數,中注次數,獲得獎勵,成功率\n';
        Object.entries(reportData.studentInvestmentStats || {}).forEach(([seat, stats]) => {
            const successRate = stats.totalBets > 0 ? (stats.successfulBets / stats.totalBets * 100).toFixed(1) : 0;
            csv += `${seat},"${stats.name}",${stats.totalBets},${stats.successfulBets},${stats.totalReward},${successRate}%\n`;
        });

        // 總結
        csv += '\n總結統計\n';
        csv += `總投資人數,${reportData.summary.totalInvestors}\n`;
        csv += `成功投資人數,${reportData.summary.successfulInvestors}\n`;
        csv += `總獎勵點數,${reportData.summary.totalRewards}\n`;
        csv += `成功率,${reportData.summary.successRate}%\n`;

        return csv;
    }

    generateExcelReport(reportData) {
        // 簡化的Excel格式，實際應用建議使用專業庫
        return this.generateCSVReport(reportData);
    }

    /**
     * 重置系統
     */
    resetSystem() {
        const confirmReset = confirm('⚠️ 確定要重置系統嗎？\n\n這將會：\n• 清除所有學生資料\n• 清除所有分數記錄\n• 清除所有投資記錄\n• 清除所有歷史記錄\n• 重置週次為第1週\n\n此操作無法復原！');

        if (!confirmReset) {
            return false;
        }

        const doubleConfirm = confirm('🚨 最後確認！\n\n您真的要清除所有數據嗎？\n建議在重置前先匯出備份。\n\n點擊「確定」將立即清除所有數據。');

        if (!doubleConfirm) {
            return false;
        }

        try {
            // 清除所有數據
            Object.values(this.storageKeys).forEach(key => {
                localStorage.removeItem(key);
            });

            // 重新初始化系統
            this.initializeData();

            return true;
        } catch (error) {
            console.error('重置系統失敗:', error);
            return false;
        }
    }
}

// 創建全局數據管理實例
window.dataManager = new DataManager();
