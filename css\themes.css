/* 暗色主題 */
[data-theme="dark"] {
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-light: #94a3b8;
    --bg-white: #1e293b;
    --bg-gray: #334155;
    --border-light: #475569;
    
    /* 暗色背景漸層 */
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

[data-theme="dark"] body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: var(--text-primary);
}

[data-theme="dark"] .loading-screen {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

[data-theme="dark"] .card {
    background: var(--bg-white);
    border-color: var(--border-light);
}

[data-theme="dark"] .admin-section {
    background: var(--bg-gray);
}

[data-theme="dark"] .student-item {
    background: var(--bg-gray);
    border-color: var(--border-light);
    color: var(--text-primary);
}

[data-theme="dark"] .student-name {
    color: var(--text-secondary);
}

[data-theme="dark"] .bet-target {
    background: var(--bg-gray);
    border-color: var(--border-light);
    color: var(--text-primary);
}

[data-theme="dark"] .bet-summary {
    background: var(--bg-gray);
}

[data-theme="dark"] .leaderboard-table th {
    background: var(--bg-gray);
    color: var(--text-primary);
}

[data-theme="dark"] .leaderboard-table tr:hover {
    background: rgba(255, 182, 213, 0.1);
}

[data-theme="dark"] .student-card {
    background: var(--bg-white);
    border-color: var(--border-light);
}

[data-theme="dark"] input,
[data-theme="dark"] select,
[data-theme="dark"] textarea {
    background: var(--bg-gray);
    border-color: var(--border-light);
    color: var(--text-primary);
}

[data-theme="dark"] input::placeholder,
[data-theme="dark"] textarea::placeholder {
    color: var(--text-light);
}

/* 動畫增強 */
.pastel-bounce {
    animation: bounce 2s infinite;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.scale-in {
    animation: scaleIn 0.2s ease-out;
}

@keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* 特殊效果 */
.glow {
    box-shadow: 0 0 20px rgba(255, 182, 213, 0.5);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 成功/錯誤狀態 */
.success-state {
    border-color: var(--success) !important;
    background: rgba(16, 185, 129, 0.1) !important;
}

.error-state {
    border-color: var(--error) !important;
    background: rgba(239, 68, 68, 0.1) !important;
}

.warning-state {
    border-color: var(--warning) !important;
    background: rgba(245, 158, 11, 0.1) !important;
}

/* 載入狀態 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-pink);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--bg-white);
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
}

/* 滾動條樣式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-gray);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-pink);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-pink);
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-gray);
}

/* 響應式字體 */
@media (max-width: 480px) {
    .main-title {
        font-size: 1.75rem;
    }
    
    .subtitle {
        font-size: 0.9rem;
    }
    
    .card-title {
        font-size: 1.25rem;
    }
    
    .card {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.9rem;
    }
}

/* 打印樣式 */
@media print {
    .header-buttons,
    .admin-panel,
    .floating-stars {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid var(--border-light);
    }
    
    body {
        background: white !important;
    }
}
