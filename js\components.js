/**
 * UI 組件模組
 * 將 React 組件轉換為原生 JavaScript 函數
 */

/**
 * 學生選擇器組件
 */
const StudentSelector = {
    render: (students, selectedStudent, onSelect) => {
        const container = DOM.get('#studentGrid');
        if (!container) return;
        
        container.innerHTML = '';
        
        if (!students.length) {
            container.innerHTML = '<div class="empty-state"><p>暫無學生資料</p></div>';
            return;
        }
        
        students.forEach(student => {
            const studentItem = DOM.create('div', 'student-item');
            studentItem.innerHTML = `
                <div class="student-seat">${student.seat_number}</div>
                <div class="student-name">${student.name}</div>
            `;
            
            // 設置選中狀態
            if (selectedStudent && selectedStudent.seat_number === student.seat_number) {
                studentItem.classList.add('selected');
            }
            
            // 點擊事件
            studentItem.addEventListener('click', () => {
                // 移除其他選中狀態
                container.querySelectorAll('.student-item').forEach(item => {
                    item.classList.remove('selected');
                });
                
                // 設置當前選中
                studentItem.classList.add('selected');
                
                // 觸發回調
                if (onSelect) onSelect(student);
            });
            
            container.appendChild(studentItem);
        });
    }
};

/**
 * 投資表單組件
 */
const BetForm = {
    selectedTargets: [],
    hasExistingBets: false,
    
    render: (students, selectedStudent, onSubmit) => {
        const container = DOM.get('#betContent');
        if (!container) return;
        
        if (!selectedStudent) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🎯</div>
                    <p>請先選擇你的座號開始投資</p>
                </div>
            `;
            return;
        }
        
        // 檢查是否已經投資過
        BetForm.checkExistingBets(selectedStudent.seat_number);
        
        if (BetForm.hasExistingBets) {
            BetForm.renderExistingBets(container, selectedStudent);
        } else {
            BetForm.renderBetForm(container, students, selectedStudent, onSubmit);
        }
    },
    
    checkExistingBets: (seatNumber) => {
        const existingBets = dataManager.getBetsByBettor(seatNumber);
        BetForm.hasExistingBets = existingBets.length > 0;
        return BetForm.hasExistingBets;
    },
    
    renderExistingBets: (container, selectedStudent) => {
        const bets = dataManager.getBetsByBettor(selectedStudent.seat_number);
        
        container.innerHTML = `
            <div class="bet-status">
                <div class="status-header">
                    <span class="status-icon">✅</span>
                    <h3>本週投資狀況</h3>
                </div>
                <p class="status-message">你已經完成本週投資！</p>
                <div class="invested-targets">
                    <h4>投資對象：</h4>
                    <div class="targets-list">
                        ${bets.map(bet => `
                            <div class="target-item">
                                <span class="target-seat">${bet.target_seat_number}</span>
                                <span class="target-name">${bet.target_name}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <button class="btn btn-secondary" id="refreshBetsBtn">
                    🔄 重新整理投資狀況
                </button>
            </div>
        `;
        
        // 重新整理按鈕事件
        DOM.get('#refreshBetsBtn').addEventListener('click', () => {
            BetForm.checkExistingBets(selectedStudent.seat_number);
            BetForm.render(dataManager.getStudents(), selectedStudent, null);
        });
    },
    
    renderBetForm: (container, students, selectedStudent, onSubmit) => {
        // 可投資的學生（排除自己）
        const availableTargets = students.filter(s => s.seat_number !== selectedStudent.seat_number);
        
        container.innerHTML = `
            <div class="bet-form">
                <div class="bet-info">
                    <p><strong>${selectedStudent.name}</strong> (座號 ${selectedStudent.seat_number})</p>
                    <p class="bet-rule">請選擇 <strong>3位同學</strong> 進行投資</p>
                </div>
                
                <div class="bet-summary" id="betSummary">
                    <h4>已選擇的投資對象 (<span id="selectedCount">0</span>/3)：</h4>
                    <div class="selected-targets" id="selectedTargets"></div>
                </div>
                
                <div class="targets-section">
                    <h4>選擇要投資的同學：</h4>
                    <div class="bet-targets" id="betTargets"></div>
                </div>
                
                <button class="btn btn-primary" id="submitBetBtn" disabled>
                    提交投資 (<span id="submitCount">0</span>/3 位同學)
                </button>
            </div>
        `;
        
        // 渲染可選目標
        BetForm.renderTargets(availableTargets);
        
        // 提交按鈕事件
        DOM.get('#submitBetBtn').addEventListener('click', () => {
            BetForm.handleSubmit(onSubmit);
        });
        
        // 更新顯示
        BetForm.updateDisplay();
    },
    
    renderTargets: (availableTargets) => {
        const container = DOM.get('#betTargets');
        if (!container) return;
        
        container.innerHTML = '';
        
        availableTargets.forEach(student => {
            const targetItem = DOM.create('div', 'bet-target');
            targetItem.innerHTML = `
                <div class="target-seat">${student.seat_number}</div>
                <div class="target-name">${student.name}</div>
            `;
            
            targetItem.addEventListener('click', () => {
                BetForm.toggleTarget(student, targetItem);
            });
            
            container.appendChild(targetItem);
        });
    },
    
    toggleTarget: (student, element) => {
        const isSelected = BetForm.selectedTargets.find(s => s.seat_number === student.seat_number);
        
        if (isSelected) {
            // 取消選擇
            BetForm.selectedTargets = BetForm.selectedTargets.filter(s => s.seat_number !== student.seat_number);
            element.classList.remove('selected');
        } else {
            // 選擇（最多3個）
            if (BetForm.selectedTargets.length < 3) {
                BetForm.selectedTargets.push(student);
                element.classList.add('selected');
            } else {
                Notification.warning('最多只能選擇3位同學！');
                return;
            }
        }
        
        BetForm.updateDisplay();
    },
    
    updateDisplay: () => {
        const selectedCount = DOM.get('#selectedCount');
        const selectedTargets = DOM.get('#selectedTargets');
        const submitBtn = DOM.get('#submitBetBtn');
        const submitCount = DOM.get('#submitCount');
        
        if (selectedCount) selectedCount.textContent = BetForm.selectedTargets.length;
        if (submitCount) submitCount.textContent = BetForm.selectedTargets.length;
        
        if (selectedTargets) {
            selectedTargets.innerHTML = BetForm.selectedTargets.map(target => `
                <div class="selected-target">
                    ${target.seat_number} - ${target.name}
                </div>
            `).join('');
        }
        
        if (submitBtn) {
            submitBtn.disabled = BetForm.selectedTargets.length !== 3;
        }
    },
    
    handleSubmit: (onSubmit) => {
        if (BetForm.selectedTargets.length !== 3) {
            Notification.error('請選擇剛好3位同學投資！');
            return;
        }
        
        const bets = BetForm.selectedTargets.map(target => ({
            targetSeatNumber: target.seat_number,
            amount: 1
        }));
        
        if (onSubmit) {
            onSubmit(bets);
        }
        
        // 重置選擇
        BetForm.selectedTargets = [];
    }
};

/**
 * 排行榜組件
 */
const Leaderboard = {
    render: (scores) => {
        const container = DOM.get('#leaderboardContent');
        if (!container) return;

        if (!scores.length) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📊</div>
                    <p>暫無分數資料</p>
                </div>
            `;
            return;
        }

        // 獲取投資者資料
        const scoresWithInvestors = scores.map(score => {
            const investors = dataManager.getBetsOnStudent(score.seat_number);
            return { ...score, investors };
        });

        container.innerHTML = `
            <div class="leaderboard-wrapper">
                <table class="leaderboard-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>座號</th>
                            <th>學生</th>
                            <th>投資者姓名</th>
                            <th>分數</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${scoresWithInvestors.map((score, index) => {
                            const rankEmoji = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '';
                            return `
                                <tr>
                                    <td>
                                        <div class="rank-cell">
                                            ${rankEmoji ? `<span class="rank-emoji">${rankEmoji}</span>` : ''}
                                            ${index + 1}
                                        </div>
                                    </td>
                                    <td><span class="seat-number">${score.seat_number}</span></td>
                                    <td>${score.name}</td>
                                    <td>
                                        <div class="investors-cell">
                                            ${score.investors.map(investor => {
                                                const investorStudent = dataManager.getStudentBySeat(investor.bettor_seat_number);
                                                return `
                                                    <span class="investor-tag" title="座號 ${investor.bettor_seat_number}">
                                                        ${investorStudent ? investorStudent.name : '座號' + investor.bettor_seat_number}
                                                    </span>
                                                `;
                                            }).join('')}
                                            ${score.investors.length === 0 ? '<span class="no-investors">無人投資</span>' : ''}
                                        </div>
                                    </td>
                                    <td><span class="score-cell">${score.score}</span></td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }
};

/**
 * 激勵面板組件
 */
const MotivationPanel = {
    render: (selectedStudent, students) => {
        const container = DOM.get('#motivationContent');
        if (!container) return;
        
        if (!selectedStudent) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">💪</div>
                    <p>選擇學生後顯示激勵訊息</p>
                </div>
            `;
            return;
        }
        
        // 獲取學生分數和排名
        const scores = dataManager.getScores();
        const studentScore = scores.find(s => s.seat_number === selectedStudent.seat_number);
        const rank = scores.findIndex(s => s.seat_number === selectedStudent.seat_number) + 1;
        
        // 生成激勵訊息
        let message = MotivationMessages.getRandom('general');
        let category = 'general';
        
        if (studentScore) {
            if (studentScore.score >= 90) {
                category = 'highScore';
            } else if (rank <= 3) {
                category = 'highScore';
            } else if (studentScore.score >= 80) {
                category = 'improvement';
            } else {
                category = 'encouragement';
            }
            message = MotivationMessages.getRandom(category);
        }
        
        container.innerHTML = `
            <div class="motivation-content">
                <div class="student-info">
                    <h3>${selectedStudent.name}</h3>
                    <p>座號 ${selectedStudent.seat_number}</p>
                    ${studentScore ? `
                        <div class="score-info">
                            <span class="current-score">目前分數: ${studentScore.score}</span>
                            <span class="current-rank">排名: 第 ${rank} 名</span>
                        </div>
                    ` : ''}
                </div>
                <div class="motivation-message">
                    <div class="message-icon">🌟</div>
                    <p class="message-text">${message}</p>
                </div>
                <button class="btn btn-accent" id="newMessageBtn">
                    🎲 換個激勵訊息
                </button>
            </div>
        `;
        
        // 換訊息按鈕事件
        DOM.get('#newMessageBtn').addEventListener('click', () => {
            const newMessage = MotivationMessages.getRandom(category);
            DOM.get('.message-text').textContent = newMessage;
        });
    }
};

/**
 * 管理員面板組件
 */
const AdminPanel = {
    currentTab: 'students',
    
    render: () => {
        AdminPanel.renderStudentsTab();
        AdminPanel.renderScoresTab();
        AdminPanel.renderSettlementTab();
        AdminPanel.renderHistoryTab();
        AdminPanel.bindTabEvents();
        AdminPanel.bindFormEvents();
    },
    
    renderStudentsTab: () => {
        const students = dataManager.getStudents();
        const container = DOM.get('#studentsList');
        if (!container) return;
        
        container.innerHTML = students.map(student => `
            <div class="student-card">
                <div class="student-info">
                    <div class="student-seat">${student.seat_number}</div>
                    <div class="student-name">${student.name}</div>
                </div>
                <button class="delete-btn" data-student-id="${student.id}">
                    刪除
                </button>
            </div>
        `).join('');
        
        // 綁定刪除事件
        container.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const studentId = parseInt(e.target.dataset.studentId);
                AdminPanel.deleteStudent(studentId);
            });
        });
    },
    
    renderScoresTab: () => {
        const students = dataManager.getStudents();
        const select = DOM.get('#scoreStudentSelect');
        if (!select) return;
        
        select.innerHTML = '<option value="">選擇學生</option>' +
            students.map(student => `
                <option value="${student.seat_number}">
                    ${student.seat_number} - ${student.name}
                </option>
            `).join('');
    },
    
    renderSettlementTab: () => {
        const currentWeek = dataManager.getCurrentWeek();
        const container = DOM.get('#settlementTab');
        if (!container) return;

        const weekInfo = container.querySelector('.admin-section p');
        if (weekInfo) {
            weekInfo.textContent = `目前是第 ${currentWeek} 週，結算後將開始第 ${currentWeek + 1} 週`;
        }
    },

    renderHistoryTab: () => {
        const rewards = dataManager.getRewards();
        const weekSelect = DOM.get('#weekSelect');
        if (!weekSelect) return;

        // 更新週次選項
        weekSelect.innerHTML = '<option value="">選擇週次</option>' +
            rewards.map(reward => `
                <option value="${reward.week}">第 ${reward.week} 週</option>
            `).join('');
    },

    viewWeekResults: (week) => {
        const container = DOM.get('#weekResults');
        if (!container || !week) return;

        const weekData = dataManager.getWeekSettlement(week);
        if (!weekData) {
            container.innerHTML = '<p>找不到該週的結算記錄</p>';
            return;
        }

        // 顯示下載按鈕
        const downloadBtn = DOM.get('#downloadHistoryBtn');
        if (downloadBtn) {
            downloadBtn.style.display = 'inline-block';
            downloadBtn.setAttribute('data-week', week);
        }

        container.innerHTML = `
            <div class="week-results">
                <h4>第 ${week} 週結算結果</h4>
                <div class="settlement-date">
                    結算時間：${new Date(weekData.settlementDate).toLocaleString('zh-TW')}
                </div>

                <div class="top3-section">
                    <h5>🏆 前三名</h5>
                    <div class="top3-list">
                        ${weekData.top3.map((student, index) => {
                            const medals = ['🥇', '🥈', '🥉'];
                            return `
                                <div class="top3-item">
                                    <span class="medal">${medals[index]}</span>
                                    <span class="rank">第${index + 1}名</span>
                                    <span class="student-info">座號${student.seat_number} ${student.name}</span>
                                    <span class="score">${student.score}分</span>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>

                <div class="rewards-section">
                    <h5>💰 投資收益</h5>
                    <div class="rewards-list">
                        ${Object.entries(weekData.investorRewards).map(([seatNumber, reward]) => {
                            const student = dataManager.getStudentBySeat(parseInt(seatNumber));
                            return `
                                <div class="reward-item">
                                    <div class="investor-info">
                                        <strong>座號${seatNumber} ${student ? student.name : '未知'}</strong>
                                        <span class="total-reward">總收益：${reward.totalReward}點</span>
                                    </div>
                                    <div class="bet-details">
                                        ${reward.allBets.map(bet => `
                                            <span class="bet-result ${bet.isWin ? 'win' : 'lose'}">
                                                ${bet.targetName} ${bet.isWin ? '✅+100' : '❌+0'}
                                            </span>
                                        `).join('')}
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>

                <div class="investment-stats-section">
                    <h5>📊 學生投資統計</h5>
                    <div class="stats-table-container">
                        <table class="investment-stats-table">
                            <thead>
                                <tr>
                                    <th>座號</th>
                                    <th>姓名</th>
                                    <th>投資次數</th>
                                    <th>中注次數</th>
                                    <th>獲得獎勵</th>
                                    <th>投資詳情</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${Object.entries(weekData.studentInvestmentStats || {}).map(([seatNumber, stats]) => `
                                    <tr>
                                        <td class="seat-number">${seatNumber}</td>
                                        <td class="student-name">${stats.name}</td>
                                        <td class="bet-count">${stats.totalBets}</td>
                                        <td class="win-count">${stats.successfulBets}</td>
                                        <td class="reward-amount">+${stats.totalReward}點</td>
                                        <td class="bet-details-cell">
                                            ${stats.betDetails.map(bet => `
                                                <span class="bet-detail ${bet.isWin ? 'win' : 'lose'}">
                                                    ${bet.targetName}${bet.isWin ? '✅' : '❌'}
                                                </span>
                                            `).join(' ')}
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    </div>
                </div>
            </div>
        `;
    },
    
    bindTabEvents: () => {
        DOM.getAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                AdminPanel.switchTab(tab);
            });
        });
    },
    
    switchTab: (tab) => {
        // 更新按鈕狀態
        DOM.getAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        DOM.get(`[data-tab="${tab}"]`).classList.add('active');
        
        // 更新內容顯示
        DOM.getAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        DOM.get(`#${tab}Tab`).classList.add('active');
        
        AdminPanel.currentTab = tab;
    },
    
    bindFormEvents: () => {
        // 新增學生
        DOM.get('#addStudentBtn').addEventListener('click', AdminPanel.addStudent);

        // 更新分數
        DOM.get('#updateScoreBtn').addEventListener('click', AdminPanel.updateScore);

        // 批量更新分數
        DOM.get('#batchUpdateBtn').addEventListener('click', AdminPanel.batchUpdateScores);

        // 週結算
        DOM.get('#settlementBtn').addEventListener('click', AdminPanel.performSettlement);

        // 數據管理
        DOM.get('#exportDataBtn').addEventListener('click', AdminPanel.exportData);
        DOM.get('#importDataBtn').addEventListener('click', () => {
            DOM.get('#importFileInput').click();
        });
        DOM.get('#importFileInput').addEventListener('change', AdminPanel.importData);

        // 歷史記錄
        DOM.get('#viewWeekBtn').addEventListener('click', () => {
            const week = parseInt(DOM.get('#weekSelect').value);
            if (week) {
                AdminPanel.viewWeekResults(week);
            } else {
                Notification.warning('請選擇要查看的週次');
            }
        });

        // 下載歷史記錄
        DOM.get('#downloadHistoryBtn').addEventListener('click', AdminPanel.downloadWeekHistory);

        // 重置系統
        DOM.get('#resetSystemBtn').addEventListener('click', AdminPanel.resetSystem);
    },
    
    addStudent: () => {
        const seatInput = DOM.get('#newStudentSeat');
        const nameInput = DOM.get('#newStudentName');
        
        const seatNumber = parseInt(seatInput.value);
        const name = nameInput.value.trim();
        
        if (!Validator.validSeatNumber(seatNumber)) {
            Notification.error('請輸入有效的座號 (1-50)');
            return;
        }
        
        if (!Validator.validStudentName(name)) {
            Notification.error('請輸入有效的學生姓名 (2-10個字符)');
            return;
        }
        
        try {
            dataManager.addStudent(seatNumber, name);
            Notification.success(`成功新增學生：${name} (座號 ${seatNumber})`);
            
            // 清空表單
            seatInput.value = '';
            nameInput.value = '';
            
            // 重新渲染
            AdminPanel.renderStudentsTab();
            AdminPanel.renderScoresTab();
            
            // 同步更新主應用的學生數據
            if (window.app) {
                window.app.refreshData();
            }
            
        } catch (error) {
            Notification.error(error.message);
        }
    },
    
    deleteStudent: (studentId) => {
        if (!confirm('確定要刪除這位學生嗎？這將同時刪除相關的分數和投注記錄。')) {
            return;
        }
        
        try {
            dataManager.deleteStudent(studentId);
            Notification.success('學生刪除成功');
            AdminPanel.renderStudentsTab();
            AdminPanel.renderScoresTab();
            
            // 同步更新主應用的學生數據
            if (window.app) {
                window.app.refreshData();
            }
        } catch (error) {
            Notification.error(error.message);
        }
    },
    
    updateScore: () => {
        const seatSelect = DOM.get('#scoreStudentSelect');
        const scoreInput = DOM.get('#newScore');
        
        const seatNumber = parseInt(seatSelect.value);
        const score = parseInt(scoreInput.value);
        
        if (!seatNumber) {
            Notification.error('請選擇學生');
            return;
        }
        
        if (!Validator.isNumber(score) || score < 0) {
            Notification.error('請輸入有效的分數');
            return;
        }
        
        try {
            dataManager.updateScore(seatNumber, score);
            Notification.success('分數更新成功');
            scoreInput.value = '';
        } catch (error) {
            Notification.error(error.message);
        }
    },
    
    batchUpdateScores: () => {
        const textarea = DOM.get('#batchScores');
        const data = textarea.value.trim();
        
        if (!data) {
            Notification.error('請輸入分數資料');
            return;
        }
        
        const lines = data.split('\n');
        let successCount = 0;
        let errorCount = 0;
        
        lines.forEach(line => {
            const [seatStr, scoreStr] = line.split(',').map(s => s.trim());
            const seatNumber = parseInt(seatStr);
            const score = parseInt(scoreStr);
            
            if (Validator.validSeatNumber(seatNumber) && Validator.isNumber(score) && score >= 0) {
                try {
                    dataManager.updateScore(seatNumber, score);
                    successCount++;
                } catch (error) {
                    errorCount++;
                }
            } else {
                errorCount++;
            }
        });
        
        if (successCount > 0) {
            Notification.success(`成功更新 ${successCount} 位學生的分數`);
        }
        if (errorCount > 0) {
            Notification.warning(`${errorCount} 筆資料更新失敗`);
        }
        
        textarea.value = '';
    },
    
    performSettlement: () => {
        if (!confirm('確定要執行週結算嗎？這將開始新的一週。')) {
            return;
        }

        try {
            const newWeek = dataManager.performWeeklySettlement();
            Notification.success(`週結算完成！現在是第 ${newWeek} 週`);
            AdminPanel.renderSettlementTab();
        } catch (error) {
            Notification.error(error.message);
        }
    },

    downloadWeekHistory: (event) => {
        const week = parseInt(event.target.getAttribute('data-week'));
        if (!week) {
            Notification.error('請先選擇要下載的週次');
            return;
        }

        try {
            // 預設下載CSV格式
            const reportData = dataManager.downloadWeekHistory(week, 'csv');
            const blob = new Blob([reportData], { type: 'text/csv;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `第${week}週結算報告_${Format.date(new Date(), 'YYYY-MM-DD')}.csv`;
            a.click();

            URL.revokeObjectURL(url);
            Notification.success(`第${week}週結算報告下載成功`);
        } catch (error) {
            console.error('下載失敗:', error);
            Notification.error('下載失敗：' + error.message);
        }
    },

    resetSystem: (event) => {
        try {
            const success = dataManager.resetSystem();
            if (success) {
                Notification.success('系統重置成功！頁面將重新載入...');

                // 延遲重新載入頁面
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                Notification.info('重置操作已取消');
            }
        } catch (error) {
            console.error('重置失敗:', error);
            Notification.error('重置失敗：' + error.message);
        }
    },
    
    exportData: () => {
        try {
            const format = DOM.get('#exportFormat').value;
            const data = dataManager.exportData(format);

            let mimeType, extension;
            switch (format) {
                case 'csv':
                    mimeType = 'text/csv';
                    extension = 'csv';
                    break;
                case 'excel':
                    mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    extension = 'xlsx';
                    break;
                default:
                    mimeType = 'application/json';
                    extension = 'json';
            }

            const blob = new Blob([data], { type: mimeType });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `betting_system_data_${Format.date(new Date(), 'YYYY-MM-DD')}.${extension}`;
            a.click();

            URL.revokeObjectURL(url);
            Notification.success(`數據導出成功 (${format.toUpperCase()} 格式)`);
        } catch (error) {
            Notification.error('數據導出失敗');
        }
    },
    
    importData: (event) => {
        const file = event.target.files[0];
        if (!file) return;

        // 根據檔案副檔名判斷格式
        const fileName = file.name.toLowerCase();
        let format = DOM.get('#importFormat').value;

        // 自動檢測格式
        if (fileName.endsWith('.json')) format = 'json';
        else if (fileName.endsWith('.csv')) format = 'csv';
        else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) format = 'excel';

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const success = dataManager.importData(e.target.result, format);
                if (success) {
                    Notification.success(`數據導入成功 (${format.toUpperCase()} 格式)`);
                    // 重新渲染所有內容
                    AdminPanel.render();

                    // 同步更新主應用的數據
                    if (window.app) {
                        window.app.refreshData();
                    }
                } else {
                    Notification.error('數據導入失敗，請檢查檔案格式');
                }
            } catch (error) {
                console.error('導入錯誤:', error);
                Notification.error(`數據格式錯誤：${error.message}`);
            }
        };

        // 根據格式選擇讀取方式
        if (format === 'excel') {
            reader.readAsArrayBuffer(file);
        } else {
            reader.readAsText(file);
        }

        // 清空文件輸入
        event.target.value = '';
    }
};

// 將組件掛載到全局
window.StudentSelector = StudentSelector;
window.BetForm = BetForm;
window.Leaderboard = Leaderboard;
window.MotivationPanel = MotivationPanel;
window.AdminPanel = AdminPanel;
