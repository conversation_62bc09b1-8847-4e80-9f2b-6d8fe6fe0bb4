/* 基礎重置和變數 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 粉色主題色彩 */
    --primary-pink: #ffb6d5;
    --light-pink: #fff7fa;
    --soft-pink: #fef9fc;
    --accent-pink: #ff8cc8;
    --dark-pink: #e699c2;
    
    /* 中性色彩 */
    --text-primary: #2d2d2d;
    --text-secondary: #666666;
    --text-light: #999999;
    --bg-white: #ffffff;
    --bg-gray: #f8f9fa;
    --border-light: #e5e7eb;
    
    /* 功能色彩 */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* 陰影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-pink: 2px 2px 4px rgba(255, 182, 213, 0.3);
    
    /* 字體 */
    --font-title: 'Architects Daughter', cursive;
    --font-body: 'Poppins', sans-serif;
    
    /* 動畫 */
    --transition: all 0.3s ease;
}

/* 基礎樣式 */
body {
    font-family: var(--font-body);
    background: linear-gradient(135deg, var(--light-pink) 0%, var(--soft-pink) 100%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

/* 浮動星星背景 */
.floating-stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-star {
    position: absolute;
    font-size: 1.5rem;
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* 載入畫面 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--light-pink) 0%, var(--soft-pink) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: var(--text-secondary);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--primary-pink);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 容器和佈局 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    position: relative;
    z-index: 2;
}

/* 頁首 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    font-size: 4rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.main-title {
    font-family: var(--font-title);
    font-size: 3rem;
    color: var(--primary-pink);
    text-shadow: var(--shadow-pink);
    margin-bottom: 0.5rem;
}

.subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    font-weight: 400;
}

.header-buttons {
    display: flex;
    gap: 0.75rem;
}

/* 按鈕樣式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-family: var(--font-body);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-pink), var(--accent-pink));
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    color: var(--text-primary);
}

.btn-accent {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning), #d97706);
    color: white;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 卡片樣式 */
.card {
    background: var(--bg-white);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
    border: 2px solid rgba(255, 182, 213, 0.1);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.card-icon {
    font-size: 2rem;
}

.card-title {
    font-family: var(--font-title);
    font-size: 1.5rem;
    color: var(--text-primary);
    margin: 0;
}

/* 表單元素 */
.form-group {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

input, select, textarea {
    padding: 0.75rem;
    border: 2px solid var(--border-light);
    border-radius: 0.5rem;
    font-family: var(--font-body);
    font-size: 1rem;
    transition: var(--transition);
    flex: 1;
    min-width: 120px;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-pink);
    box-shadow: 0 0 0 3px rgba(255, 182, 213, 0.1);
}

textarea {
    resize: vertical;
    min-height: 100px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .header {
        flex-direction: column;
        text-align: center;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .form-group {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 空狀態 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-light);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.empty-state p {
    font-size: 1.1rem;
    font-weight: 400;
}

/* 管理員面板 */
.admin-panel {
    margin-bottom: 2rem;
}

.admin-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid var(--border-light);
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    font-family: var(--font-body);
    font-size: 1rem;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: var(--transition);
}

.tab-btn.active {
    color: var(--primary-pink);
    border-bottom-color: var(--primary-pink);
    font-weight: 600;
}

.tab-btn:hover {
    color: var(--primary-pink);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.admin-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-gray);
    border-radius: 1rem;
}

.admin-section h3 {
    font-family: var(--font-title);
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

/* 學生網格 */
.student-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem;
}

.student-item {
    padding: 1rem;
    background: var(--bg-gray);
    border: 2px solid var(--border-light);
    border-radius: 0.75rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.student-item:hover {
    border-color: var(--primary-pink);
    background: rgba(255, 182, 213, 0.1);
}

.student-item.selected {
    background: var(--primary-pink);
    color: white;
    border-color: var(--primary-pink);
    box-shadow: var(--shadow-md);
}

.student-seat {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-pink);
    margin-bottom: 0.25rem;
}

.student-item.selected .student-seat {
    color: white;
}

.student-name {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.student-item.selected .student-name {
    color: white;
}

/* 主要內容佈局 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.left-panel, .right-panel {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* 投資表單 */
.bet-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.bet-info {
    background: var(--bg-gray);
    padding: 1rem;
    border-radius: 0.75rem;
    text-align: center;
}

.bet-info p:first-child {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-pink);
    margin-bottom: 0.5rem;
}

.bet-rule {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.bet-status {
    text-align: center;
    padding: 2rem;
}

.status-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.status-icon {
    font-size: 2rem;
}

.status-header h3 {
    font-family: var(--font-title);
    color: var(--text-primary);
    margin: 0;
}

.status-message {
    color: var(--success);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.invested-targets h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-family: var(--font-title);
}

.targets-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.target-item {
    background: var(--success);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.target-seat {
    font-weight: 700;
}

.targets-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-family: var(--font-title);
}

.bet-targets {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
    max-height: 250px;
    overflow-y: auto;
    margin-bottom: 1.5rem;
}

.bet-target {
    padding: 0.5rem;
    background: var(--bg-gray);
    border: 2px solid var(--border-light);
    border-radius: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.8rem;
}

.bet-target:hover {
    border-color: var(--success);
    background: rgba(16, 185, 129, 0.1);
}

.bet-target.selected {
    background: var(--success);
    color: white;
    border-color: var(--success);
}

.bet-target.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.target-seat {
    font-weight: 700;
    color: var(--primary-pink);
    margin-bottom: 0.25rem;
}

.target-name {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.bet-target.selected .target-seat,
.bet-target.selected .target-name {
    color: white;
}

.bet-summary {
    background: var(--bg-gray);
    padding: 1rem;
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
}

.bet-summary h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-family: var(--font-title);
}

.selected-targets {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.selected-target {
    background: var(--success);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
}

/* 排行榜 */
.leaderboard-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.leaderboard-table th,
.leaderboard-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.leaderboard-table th {
    background: var(--bg-gray);
    font-weight: 600;
    color: var(--text-primary);
    font-family: var(--font-body);
}

.leaderboard-table tr:hover {
    background: rgba(255, 182, 213, 0.05);
}

.rank-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.rank-emoji {
    font-size: 1.25rem;
}

.seat-number {
    font-weight: 700;
    color: var(--primary-pink);
}

.score-cell {
    text-align: right;
    font-weight: 600;
    color: var(--success);
}

.investors-cell {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.investor-tag {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    padding: 0.125rem 0.5rem;
    border-radius: 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.no-investors {
    color: var(--text-light);
    font-style: italic;
    font-size: 0.8rem;
}

/* 激勵面板 */
.motivation-content {
    text-align: center;
    padding: 1rem;
}

.student-info {
    background: var(--bg-gray);
    padding: 1.5rem;
    border-radius: 1rem;
    margin-bottom: 1.5rem;
}

.student-info h3 {
    font-family: var(--font-title);
    color: var(--primary-pink);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.student-info p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.score-info {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.current-score,
.current-rank {
    background: var(--primary-pink);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.9rem;
    font-weight: 600;
}

.current-rank {
    background: var(--success);
}

.motivation-message {
    background: linear-gradient(135deg, var(--light-pink), var(--soft-pink));
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 1.5rem;
    border: 2px solid rgba(255, 182, 213, 0.2);
}

.message-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.message-text {
    font-size: 1.1rem;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.6;
    margin: 0;
}

/* 學生列表（管理員） */
.students-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.student-card {
    background: white;
    padding: 1rem;
    border-radius: 0.75rem;
    border: 2px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.student-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.student-card .student-seat {
    font-size: 1rem;
    font-weight: 700;
    color: var(--primary-pink);
}

.student-card .student-name {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.delete-btn {
    background: var(--error);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.delete-btn:hover {
    background: #dc2626;
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }

    .admin-tabs {
        flex-wrap: wrap;
    }

    .student-grid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    }

    .bet-targets {
        grid-template-columns: repeat(5, 1fr);
    }

    .students-list {
        grid-template-columns: 1fr;
    }
}

/* 歷史記錄樣式 */
.week-results {
    margin-top: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 1rem;
    border: 2px solid var(--border-light);
}

.week-results h4 {
    color: var(--primary-pink);
    font-family: var(--font-title);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.settlement-date {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 2rem;
}

.top3-section,
.rewards-section {
    margin-bottom: 2rem;
}

.top3-section h5,
.rewards-section h5 {
    color: var(--text-primary);
    font-family: var(--font-title);
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.top3-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.top3-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-gray);
    border-radius: 0.75rem;
    border-left: 4px solid var(--primary-pink);
}

.medal {
    font-size: 1.5rem;
}

.rank {
    font-weight: 700;
    color: var(--primary-pink);
    min-width: 60px;
}

.student-info {
    flex: 1;
    font-weight: 600;
}

.score {
    font-weight: 700;
    color: var(--success);
    font-size: 1.1rem;
}

.rewards-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.reward-item {
    padding: 1rem;
    background: var(--bg-gray);
    border-radius: 0.75rem;
    border-left: 4px solid var(--success);
}

.investor-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.total-reward {
    font-weight: 700;
    color: var(--success);
    font-size: 1.1rem;
}

.bet-details {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.bet-result {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.bet-result.win {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid var(--success);
}

.bet-result.lose {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
    border: 1px solid var(--error);
}

@media (max-width: 768px) {
    .top3-item {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .investor-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* 投資統計表格樣式 */
.investment-stats-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid var(--border-light);
}

.investment-stats-section h5 {
    color: var(--text-primary);
    font-family: var(--font-title);
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.stats-table-container {
    overflow-x: auto;
    border-radius: 0.75rem;
    border: 2px solid var(--border-light);
    background: white;
}

.investment-stats-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.investment-stats-table th {
    background: var(--primary-pink);
    color: white;
    padding: 1rem 0.75rem;
    text-align: center;
    font-weight: 700;
    font-family: var(--font-title);
}

.investment-stats-table td {
    padding: 0.75rem;
    text-align: center;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.investment-stats-table tbody tr:hover {
    background: var(--bg-gray);
}

.investment-stats-table .seat-number {
    font-weight: 700;
    color: var(--primary-pink);
}

.investment-stats-table .student-name {
    font-weight: 600;
}

.investment-stats-table .bet-count,
.investment-stats-table .win-count {
    font-weight: 700;
    font-size: 1.1rem;
}

.investment-stats-table .reward-amount {
    color: var(--success);
    font-weight: 700;
    font-size: 1.1rem;
}

.investment-stats-table .bet-details-cell {
    text-align: left;
    max-width: 200px;
    padding: 0.5rem;
}

.bet-detail {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    border-radius: 0.375rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.bet-detail.win {
    background: var(--success);
    color: white;
}

.bet-detail.lose {
    background: var(--error);
    color: white;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .investment-stats-table {
        font-size: 0.8rem;
    }

    .investment-stats-table th,
    .investment-stats-table td {
        padding: 0.5rem 0.25rem;
    }

    .bet-details-cell {
        max-width: 120px;
    }

    .bet-detail {
        font-size: 0.7rem;
        padding: 0.125rem 0.25rem;
    }
}

/* 重置系統樣式 */
.reset-warning {
    display: block;
    margin-top: 0.5rem;
    color: var(--error);
    font-size: 0.85rem;
    font-weight: 500;
}

#downloadHistoryBtn {
    margin-left: 0.5rem;
}

#resetSystemBtn {
    background: var(--error);
    border-color: var(--error);
    color: white;
}

#resetSystemBtn:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
}

/* 歷史記錄控制區域 */
.form-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.form-group:last-child {
    flex-direction: column;
    align-items: flex-start;
}
