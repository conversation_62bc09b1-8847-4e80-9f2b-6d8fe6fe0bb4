# 學生答題投資系統 - HTML版本

這是一個純 HTML、CSS、JavaScript 實現的學生投資系統，適合教學使用。

## 功能特色

- 🎯 **投資系統**: 學生可以選擇3位同學進行投資
- 📊 **即時排行榜**: 顯示本週學生分數排名
- 🌙 **明暗模式**: 支援明暗主題切換
- 📱 **響應式設計**: 適配各種螢幕尺寸
- 💾 **本地存儲**: 使用 localStorage 保存數據，無需服務器

## 技術架構

- **HTML5** - 語義化標記
- **CSS3** - 現代樣式和響應式設計
- **JavaScript ES6+** - 原生 JavaScript，無框架依賴
- **localStorage** - 本地數據存儲

## 快速開始

1. 直接用瀏覽器打開 `index.html` 文件
2. 無需安裝任何依賴或啟動服務器
3. 所有數據保存在瀏覽器本地存儲中

## 文件結構

```
html-version/
├── index.html          # 主頁面
├── css/
│   ├── styles.css      # 主樣式文件
│   └── themes.css      # 主題相關樣式
├── js/
│   ├── app.js          # 主應用邏輯
│   ├── data.js         # 數據管理模組
│   ├── components.js   # UI 組件
│   └── utils.js        # 工具函數
└── README.md           # 使用說明
```

## 教學用途

這個版本特別適合：
- Web 前端開發教學
- JavaScript 程式設計課程
- HTML/CSS 實作練習
- 學生專案展示

## 瀏覽器支持

支援所有現代瀏覽器：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 使用說明

### 學生操作
1. **選擇座號**: 點擊學生選擇器中的座號卡片（26位學生）
2. **進行投資**: 選擇3位同學進行投資（不能選擇自己）
3. **查看排行榜**: 觀看本週分數排名和投資者姓名
4. **獲得激勵**: 查看個人化的激勵訊息
5. **查看投資者**: 在排行榜中可以看到投資自己的同學姓名

### 教師操作
1. **進入管理員模式**: 點擊右上角的「管理員」按鈕
2. **學生管理**: 新增、刪除學生（預設26位學生）
3. **分數管理**: 更新個別學生分數或批量更新
4. **週結算**: 自動計算投資收益，前3名的投資者各得100點
5. **歷史記錄**: 查看每週的結算結果和投資收益詳情
6. **數據管理**: 匯出/匯入系統數據

### 系統特色
- **無需安裝**: 直接用瀏覽器打開即可使用
- **數據持久化**: 使用瀏覽器本地存儲，數據不會丟失
- **響應式設計**: 支援手機、平板、電腦等各種設備
- **主題切換**: 支援明暗兩種主題模式

## 技術特點

### 模組化設計
- `data.js`: 數據管理，處理學生、分數、投注記錄
- `components.js`: UI 組件，包含所有界面元素
- `utils.js`: 工具函數，提供各種輔助功能
- `app.js`: 主應用程式，整合所有模組

### 現代 JavaScript 特性
- ES6+ 語法
- 類別和模組化
- 異步處理
- 事件驅動架構

### CSS 設計
- CSS 變數系統
- Flexbox 和 Grid 佈局
- 動畫和過渡效果
- 響應式設計

## 開發說明

### 擴展功能
如需添加新功能，請遵循以下步驟：
1. 在 `data.js` 中添加數據管理邏輯
2. 在 `components.js` 中創建 UI 組件
3. 在 `app.js` 中整合新功能
4. 在 `styles.css` 中添加樣式

### 自定義樣式
修改 CSS 變數來改變主題色彩：
```css
:root {
    --primary-pink: #your-color;
    --accent-pink: #your-color;
    /* 其他變數... */
}
```

### 數據結構
系統使用以下數據結構：
- **學生**: `{id, seat_number, name, created_at}`
- **分數**: `{id, seat_number, score, week, created_at}`
- **投注**: `{id, bettor_seat_number, target_seat_number, amount, week, created_at}`

## 故障排除

### 常見問題
1. **數據丟失**: 檢查瀏覽器是否支援 localStorage
2. **樣式異常**: 確保 CSS 文件正確載入
3. **功能異常**: 打開瀏覽器開發者工具查看錯誤訊息

### 重置系統
如需重置所有數據，可在瀏覽器控制台執行：
```javascript
dataManager.clearAllData();
location.reload();
```
