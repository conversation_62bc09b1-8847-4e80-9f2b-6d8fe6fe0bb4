/**
 * 主應用程式
 * 整合所有功能模組，管理應用狀態和事件
 */

class BettingSystemApp {
    constructor() {
        this.state = {
            students: [],
            scores: [],
            selectedStudent: null,
            isDark: false,
            showAdmin: false,
            loading: true
        };
        
        this.init();
    }
    
    /**
     * 初始化應用程式
     */
    async init() {
        try {
            // 顯示載入畫面
            this.showLoading();
            
            // 初始化主題
            this.initTheme();
            
            // 創建浮動星星
            FloatingStars.create();
            
            // 載入數據
            await this.loadData();
            
            // 綁定事件
            this.bindEvents();
            
            // 渲染界面
            this.render();
            
            // 隱藏載入畫面
            setTimeout(() => {
                this.hideLoading();
            }, 1000);
            
        } catch (error) {
            console.error('應用程式初始化失敗:', error);
            Notification.error('應用程式載入失敗，請重新整理頁面');
        }
    }
    
    /**
     * 顯示載入畫面
     */
    showLoading() {
        DOM.show('#loadingScreen');
        DOM.hide('#mainContainer');
    }
    
    /**
     * 隱藏載入畫面
     */
    hideLoading() {
        Animation.fadeOut('#loadingScreen');
        DOM.show('#mainContainer');
        Animation.fadeIn('#mainContainer');
        this.state.loading = false;
    }
    
    /**
     * 初始化主題
     */
    initTheme() {
        // 從本地存儲讀取主題設置
        const savedTheme = localStorage.getItem('betting_system_theme');
        if (savedTheme === 'dark') {
            this.state.isDark = true;
            document.documentElement.setAttribute('data-theme', 'dark');
        }
        
        this.updateThemeButton();
    }
    
    /**
     * 載入數據
     */
    async loadData() {
        try {
            this.state.students = dataManager.getStudents();
            this.state.scores = dataManager.getScores();
        } catch (error) {
            console.error('載入數據失敗:', error);
            throw error;
        }
    }
    
    /**
     * 綁定事件
     */
    bindEvents() {
        // 管理員切換按鈕
        DOM.get('#adminToggle').addEventListener('click', () => {
            this.toggleAdmin();
        });
        
        // 主題切換按鈕
        DOM.get('#themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // 窗口大小變化時重新創建星星
        window.addEventListener('resize', debounce(() => {
            FloatingStars.create();
        }, 300));
        
        // 定期重新整理數據
        setInterval(() => {
            if (!this.state.loading && !this.state.showAdmin) {
                this.refreshData();
            }
        }, 30000); // 每30秒重新整理一次
    }
    
    /**
     * 渲染界面
     */
    render() {
        if (this.state.showAdmin) {
            this.renderAdminPanel();
        } else {
            this.renderGamePanel();
        }
    }
    
    /**
     * 渲染管理員面板
     */
    renderAdminPanel() {
        DOM.show('#adminPanel');
        DOM.hide('#gamePanel');
        
        AdminPanel.render();
    }
    
    /**
     * 渲染遊戲面板
     */
    renderGamePanel() {
        DOM.hide('#adminPanel');
        DOM.show('#gamePanel');
        
        // 渲染學生選擇器
        StudentSelector.render(
            this.state.students,
            this.state.selectedStudent,
            (student) => this.handleStudentSelect(student)
        );
        
        // 渲染投資表單
        BetForm.render(
            this.state.students,
            this.state.selectedStudent,
            (bets) => this.handleBetSubmit(bets)
        );
        
        // 渲染排行榜
        Leaderboard.render(this.state.scores);
        
        // 渲染激勵面板
        MotivationPanel.render(this.state.selectedStudent, this.state.students);
    }
    
    /**
     * 切換管理員面板
     */
    toggleAdmin() {
        this.state.showAdmin = !this.state.showAdmin;
        
        const toggleBtn = DOM.get('#adminToggleText');
        if (toggleBtn) {
            toggleBtn.textContent = this.state.showAdmin ? '🎮 返回遊戲' : '⚙️ 管理員';
        }
        
        this.render();
        
        // 如果切換到遊戲面板，重新整理數據
        if (!this.state.showAdmin) {
            this.refreshData();
        }
    }
    
    /**
     * 切換主題
     */
    toggleTheme() {
        this.state.isDark = !this.state.isDark;
        
        if (this.state.isDark) {
            document.documentElement.setAttribute('data-theme', 'dark');
            localStorage.setItem('betting_system_theme', 'dark');
        } else {
            document.documentElement.removeAttribute('data-theme');
            localStorage.setItem('betting_system_theme', 'light');
        }
        
        this.updateThemeButton();
    }
    
    /**
     * 更新主題按鈕
     */
    updateThemeButton() {
        const toggleBtn = DOM.get('#themeToggleText');
        if (toggleBtn) {
            toggleBtn.textContent = this.state.isDark ? '🌞 亮色' : '🌙 暗色';
        }
    }
    
    /**
     * 處理學生選擇
     */
    handleStudentSelect(student) {
        this.state.selectedStudent = student;
        
        // 重新渲染相關組件
        BetForm.render(
            this.state.students,
            this.state.selectedStudent,
            (bets) => this.handleBetSubmit(bets)
        );
        
        MotivationPanel.render(this.state.selectedStudent, this.state.students);
        
        Notification.success(`已選擇 ${student.name} (座號 ${student.seat_number})`);
    }
    
    /**
     * 處理投資提交
     */
    async handleBetSubmit(bets) {
        if (!this.state.selectedStudent) {
            Notification.error('請先選擇你的座號');
            return;
        }
        
        try {
            // 顯示載入狀態
            const submitBtn = DOM.get('#submitBetBtn');
            if (submitBtn) {
                submitBtn.classList.add('loading');
                submitBtn.disabled = true;
            }
            
            // 提交投資
            dataManager.submitBets(this.state.selectedStudent.seat_number, bets);
            
            Notification.success(
                `投資成功！${this.state.selectedStudent.name} 已選擇 ${bets.length} 位同學`
            );
            
            // 重新整理數據和界面
            this.refreshData();
            this.render();
            
        } catch (error) {
            console.error('投資失敗:', error);
            Notification.error(error.message || '投資失敗，請稍後再試');
        } finally {
            // 移除載入狀態
            const submitBtn = DOM.get('#submitBetBtn');
            if (submitBtn) {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            }
        }
    }
    
    /**
     * 重新整理數據
     */
    async refreshData() {
        try {
            await this.loadData();
            
            // 如果在遊戲面板，重新渲染
            if (!this.state.showAdmin) {
                this.renderGamePanel();
            }
            
        } catch (error) {
            console.error('重新整理數據失敗:', error);
        }
    }
    
    /**
     * 處理錯誤
     */
    handleError(error, message = '操作失敗') {
        console.error(error);
        Notification.error(message);
    }
    
    /**
     * 獲取應用狀態
     */
    getState() {
        return { ...this.state };
    }
    
    /**
     * 更新應用狀態
     */
    setState(newState) {
        this.state = { ...this.state, ...newState };
        this.render();
    }
}

/**
 * 應用程式入口點
 */
document.addEventListener('DOMContentLoaded', () => {
    // 檢查瀏覽器支援
    if (!window.localStorage) {
        alert('您的瀏覽器不支援本地存儲功能，應用程式可能無法正常運作。');
        return;
    }
    
    // 創建應用程式實例
    window.app = new BettingSystemApp();
    
    // 全局錯誤處理
    window.addEventListener('error', (event) => {
        console.error('全局錯誤:', event.error);
        Notification.error('發生未預期的錯誤，請重新整理頁面');
    });
    
    // 全局未處理的 Promise 拒絕
    window.addEventListener('unhandledrejection', (event) => {
        console.error('未處理的 Promise 拒絕:', event.reason);
        Notification.error('操作失敗，請稍後再試');
    });
});

/**
 * 頁面卸載前的清理工作
 */
window.addEventListener('beforeunload', () => {
    // 這裡可以添加清理邏輯，比如保存未完成的操作等
    console.log('應用程式即將關閉');
});

/**
 * 頁面可見性變化處理
 */
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.app) {
        // 頁面重新可見時重新整理數據
        window.app.refreshData();
    }
});
