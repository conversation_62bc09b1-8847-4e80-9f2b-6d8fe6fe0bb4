<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>學生答題投資系統</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/themes.css">
    <link href="https://fonts.googleapis.com/css2?family=Architects+Daughter&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 浮動星星背景 -->
    <div class="floating-stars" id="floatingStars"></div>
    
    <!-- 載入畫面 -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>載入中...</p>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="container" id="mainContainer" style="display: none;">
        <!-- 頁首 -->
        <header class="header">
            <div class="header-left">
                <div class="header-icon">🌟</div>
                <div class="header-text">
                    <h1 class="main-title">學生答題投資系統</h1>
                    <p class="subtitle">答越多賺越多 💰 一起來投資同學吧！</p>
                </div>
            </div>
            <div class="header-buttons">
                <button class="btn btn-secondary" id="adminToggle">
                    <span id="adminToggleText">⚙️ 管理員</span>
                </button>
                <button class="btn btn-accent" id="themeToggle">
                    <span id="themeToggleText">🌙 暗色</span>
                </button>
            </div>
        </header>

        <!-- 管理員面板 -->
        <div class="admin-panel" id="adminPanel" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <span class="card-icon">⚙️</span>
                    <h2 class="card-title">管理員面板</h2>
                </div>
                <div class="admin-tabs">
                    <button class="tab-btn active" data-tab="students">學生管理</button>
                    <button class="tab-btn" data-tab="scores">分數管理</button>
                    <button class="tab-btn" data-tab="settlement">結算系統</button>
                    <button class="tab-btn" data-tab="history">歷史記錄</button>
                </div>
                
                <!-- 學生管理標籤 -->
                <div class="tab-content active" id="studentsTab">
                    <div class="admin-section">
                        <h3>新增學生</h3>
                        <div class="form-group">
                            <input type="number" id="newStudentSeat" placeholder="座號" min="1" max="50">
                            <input type="text" id="newStudentName" placeholder="學生姓名">
                            <button class="btn btn-primary" id="addStudentBtn">新增學生</button>
                        </div>
                    </div>
                    <div class="admin-section">
                        <h3>學生列表</h3>
                        <div class="students-list" id="studentsList"></div>
                    </div>
                </div>

                <!-- 分數管理標籤 -->
                <div class="tab-content" id="scoresTab">
                    <div class="admin-section">
                        <h3>更新分數</h3>
                        <div class="form-group">
                            <select id="scoreStudentSelect">
                                <option value="">選擇學生</option>
                            </select>
                            <input type="number" id="newScore" placeholder="新分數" min="0">
                            <button class="btn btn-primary" id="updateScoreBtn">更新分數</button>
                        </div>
                    </div>
                    <div class="admin-section">
                        <h3>批量更新</h3>
                        <div class="form-group">
                            <textarea id="batchScores" placeholder="格式：座號,分數&#10;例如：&#10;1,85&#10;2,92&#10;3,78" rows="5"></textarea>
                            <button class="btn btn-primary" id="batchUpdateBtn">批量更新</button>
                        </div>
                    </div>
                </div>

                <!-- 結算系統標籤 -->
                <div class="tab-content" id="settlementTab">
                    <div class="admin-section">
                        <h3>週結算</h3>
                        <p>結算本週投資收益並開始新的一週</p>
                        <button class="btn btn-warning" id="settlementBtn">執行週結算</button>
                    </div>
                    <div class="admin-section">
                        <h3>數據管理</h3>
                        <div class="form-group">
                            <select id="exportFormat">
                                <option value="json">JSON 格式</option>
                                <option value="csv">CSV 格式</option>
                                <option value="excel">Excel 格式</option>
                            </select>
                            <button class="btn btn-secondary" id="exportDataBtn">匯出數據</button>
                        </div>
                        <div class="form-group">
                            <select id="importFormat">
                                <option value="json">JSON 格式</option>
                                <option value="csv">CSV 格式</option>
                                <option value="excel">Excel 格式</option>
                            </select>
                            <button class="btn btn-secondary" id="importDataBtn">匯入數據</button>
                            <input type="file" id="importFileInput" accept=".json,.csv,.xlsx,.xls" style="display: none;">
                        </div>
                    </div>
                </div>

                <!-- 歷史記錄標籤 -->
                <div class="tab-content" id="historyTab">
                    <div class="admin-section">
                        <h3>週結算歷史</h3>
                        <div class="form-group">
                            <select id="weekSelect">
                                <option value="">選擇週次</option>
                            </select>
                            <button class="btn btn-primary" id="viewWeekBtn">查看結果</button>
                            <button class="btn btn-secondary" id="downloadHistoryBtn" style="display: none;">📥 下載結算報告</button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-danger" id="resetSystemBtn">🔄 重置系統</button>
                            <span class="reset-warning">⚠️ 重置將清除所有數據，請謹慎操作</span>
                        </div>
                        <div id="weekResults"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 遊戲面板 -->
        <div class="game-panel" id="gamePanel">
            <!-- 學生選擇器 -->
            <div class="card" id="studentSelector">
                <div class="card-header">
                    <span class="card-icon">👤</span>
                    <h2 class="card-title">選擇你的座號</h2>
                </div>
                <div class="student-grid" id="studentGrid"></div>
            </div>

            <!-- 主要內容區域 -->
            <div class="main-content">
                <!-- 左側：投資表單和激勵面板 -->
                <div class="left-panel">
                    <!-- 投資表單 -->
                    <div class="card" id="betForm">
                        <div class="card-header">
                            <span class="card-icon">💰</span>
                            <h2 class="card-title">投資區</h2>
                        </div>
                        <div class="bet-content" id="betContent">
                            <div class="empty-state">
                                <div class="empty-icon">🎯</div>
                                <p>請先選擇你的座號開始投資</p>
                            </div>
                        </div>
                    </div>

                    <!-- 激勵面板 -->
                    <div class="card" id="motivationPanel">
                        <div class="card-header">
                            <span class="card-icon">🎉</span>
                            <h2 class="card-title">加油打氣</h2>
                        </div>
                        <div class="motivation-content" id="motivationContent">
                            <div class="empty-state">
                                <div class="empty-icon">💪</div>
                                <p>選擇學生後顯示激勵訊息</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右側：排行榜 -->
                <div class="right-panel">
                    <div class="card" id="leaderboard">
                        <div class="card-header">
                            <span class="card-icon">🏆</span>
                            <h2 class="card-title">本週排行榜</h2>
                        </div>
                        <div class="leaderboard-content" id="leaderboardContent">
                            <div class="empty-state">
                                <div class="empty-icon">📊</div>
                                <p>載入排行榜中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 模組 -->
    <script src="js/utils.js"></script>
    <script src="js/data.js"></script>
    <script src="js/components.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
